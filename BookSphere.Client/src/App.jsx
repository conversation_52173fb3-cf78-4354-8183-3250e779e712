import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import Layout from './components/layout/Layout';
import StaffLayout from './components/layout/StaffLayout';
import HomePage from './pages/HomePage';
import BooksPage from './pages/BooksPage';
import BookDetailsPage from './pages/BookDetailsPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import OrdersPage from './pages/OrdersPage';
import WhitelistPage from './pages/WhitelistPage';
import ReviewFormPage from './pages/ReviewFormPage';

// Staff pages
import StaffDashboardPage from './pages/staff/StaffDashboardPage';
import StaffOrdersPage from './pages/staff/StaffOrdersPage';
import StaffOrderDetailsPage from './pages/staff/StaffOrderDetailsPage';
import OrderVerificationPage from './pages/staff/OrderVerificationPage';

// Admin pages
import AdminBooksPage from './pages/admin/AdminBooksPage';
import BookFormPage from './pages/admin/BookFormPage';
import InventoryPage from './pages/admin/InventoryPage';
import AnnouncementsPage from './pages/admin/AnnouncementsPage';
import UsersPage from './pages/admin/UsersPage';

// Protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return children;
};

// Staff route component
const StaffRoute = ({ children }) => {
  const { isAuthenticated, isStaff, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (!isStaff) {
    return <Navigate to="/" />;
  }

  return children;
};

// Admin route component
const AdminRoute = ({ children }) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (!isAdmin) {
    return <Navigate to="/" />;
  }

  return children;
};

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/books" element={<BooksPage />} />
          <Route path="/books/:id" element={<BookDetailsPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />

          {/* Protected routes */}
          <Route
            path="/cart"
            element={
              <ProtectedRoute>
                <CartPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/checkout"
            element={
              <ProtectedRoute>
                <CheckoutPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/orders"
            element={
              <ProtectedRoute>
                <OrdersPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/whitelist"
            element={
              <ProtectedRoute>
                <WhitelistPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/books/:id/review"
            element={
              <ProtectedRoute>
                <ReviewFormPage />
              </ProtectedRoute>
            }
          />

          {/* Catch-all route for 404 */}
          {/* Staff routes */}
          <Route path="/staff" element={
            <StaffRoute>
              <StaffLayout>
                <StaffDashboardPage />
              </StaffLayout>
            </StaffRoute>
          } />
          <Route path="/staff/orders" element={
            <StaffRoute>
              <StaffLayout>
                <StaffOrdersPage />
              </StaffLayout>
            </StaffRoute>
          } />
          <Route path="/staff/orders/:id" element={
            <StaffRoute>
              <StaffLayout>
                <StaffOrderDetailsPage />
              </StaffLayout>
            </StaffRoute>
          } />
          <Route path="/staff/verify" element={
            <StaffRoute>
              <StaffLayout>
                <OrderVerificationPage />
              </StaffLayout>
            </StaffRoute>
          } />

          {/* Admin routes */}
          <Route path="/staff/books" element={
            <AdminRoute>
              <StaffLayout>
                <AdminBooksPage />
              </StaffLayout>
            </AdminRoute>
          } />
          <Route path="/staff/books/new" element={
            <AdminRoute>
              <StaffLayout>
                <BookFormPage />
              </StaffLayout>
            </AdminRoute>
          } />
          <Route path="/staff/books/:id/edit" element={
            <AdminRoute>
              <StaffLayout>
                <BookFormPage />
              </StaffLayout>
            </AdminRoute>
          } />
          <Route path="/staff/inventory" element={
            <AdminRoute>
              <StaffLayout>
                <InventoryPage />
              </StaffLayout>
            </AdminRoute>
          } />
          <Route path="/staff/announcements" element={
            <AdminRoute>
              <StaffLayout>
                <AnnouncementsPage />
              </StaffLayout>
            </AdminRoute>
          } />
          <Route path="/staff/users" element={
            <AdminRoute>
              <StaffLayout>
                <UsersPage />
              </StaffLayout>
            </AdminRoute>
          } />

          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App
