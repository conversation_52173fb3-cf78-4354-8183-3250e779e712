import { useState, useEffect } from 'react';
import signalRService from '../../services/signalRService';

function Notifications() {
  const [notifications, setNotifications] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  useEffect(() => {
    // Start SignalR connection
    signalRService.start().catch(console.error);

    // Subscribe to all notifications
    signalRService.subscribeToAll().catch(console.error);

    // Register notification handler
    const unsubscribeNotification = signalRService.onNotification((notification) => {
      setNotifications(prev => [notification, ...prev].slice(0, 5));
    });

    // Register announcement handler
    const unsubscribeAnnouncement = signalRService.onAnnouncement((announcement) => {
      setNotifications(prev => [{
        type: 'announcement',
        title: announcement.title,
        message: announcement.content,
        timestamp: announcement.timestamp
      }, ...prev].slice(0, 5));
    });

    // Register connection status handler
    const unsubscribeConnection = signalRService.onConnectionChange((status) => {
      setConnectionStatus(status);
    });

    // Cleanup on unmount
    return () => {
      unsubscribeNotification();
      unsubscribeAnnouncement();
      unsubscribeConnection();
      signalRService.stop().catch(console.error);
    };
  }, []);

  const removeNotification = (index) => {
    setNotifications(prev => prev.filter((_, i) => i !== index));
  };

  if (notifications.length === 0) {
    return null;
  }

  const getNotificationStyles = (type) => {
    switch (type) {
      case 'system':
        return 'border-l-4 border-blue-500';
      case 'order':
        return 'border-l-4 border-green-500';
      case 'announcement':
        return 'border-l-4 border-yellow-500';
      case 'new_arrival':
        return 'border-l-4 border-purple-500';
      case 'discount':
        return 'border-l-4 border-red-500';
      default:
        return 'border-l-4 border-gray-500';
    }
  };

  return (
    <div className="fixed top-5 right-5 z-50 w-full max-w-sm space-y-3">
      {notifications.map((notification, index) => (
        <div
          key={index}
          className={`bg-white rounded-md shadow-lg overflow-hidden ${getNotificationStyles(notification.type)} animate-slide-in`}
        >
          <div className="p-4 flex justify-between">
            <div className="flex-1 pr-3">
              {notification.title && (
                <h4 className="font-medium text-gray-900 mb-1">{notification.title}</h4>
              )}
              <p className="text-sm text-gray-700 mb-1">{notification.message}</p>
              <p className="text-xs text-gray-500">
                {new Date(notification.timestamp).toLocaleTimeString()}
              </p>
            </div>
            <button
              className="text-gray-400 hover:text-gray-600"
              onClick={() => removeNotification(index)}
              aria-label="Close notification"
            >
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}

export default Notifications;
