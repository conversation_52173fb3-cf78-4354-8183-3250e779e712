import { Link } from 'react-router-dom';

function Footer() {
  return (
    <footer className="bg-gray-100 border-t border-gray-200">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">BookSphere</h3>
            <p className="text-gray-600">Your online book store with a wide selection of books for all readers.</p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-600 hover:text-blue-600">Home</Link>
              </li>
              <li>
                <Link to="/books" className="text-gray-600 hover:text-blue-600">Books</Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-600 hover:text-blue-600">About</Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-600 hover:text-blue-600">Contact</Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Contact</h3>
            <p className="text-gray-600 mb-2">Email: <EMAIL></p>
            <p className="text-gray-600">Phone: (*************</p>
          </div>
        </div>
      </div>

      <div className="border-t border-gray-200 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-sm text-center text-gray-500">&copy; {new Date().getFullYear()} BookSphere. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
