import Header from './Header';
import Footer from './Footer';
import Notifications from '../common/Notifications';
import AnimatedBackground from '../ui/AnimatedBackground';
import PageTransition from '../ui/PageTransition';

function Layout({ children }) {
  return (
    <div className="flex flex-col min-h-screen relative overflow-hidden">
      <AnimatedBackground />
      <Header />
      <Notifications />
      <PageTransition>
        <main className="flex-grow py-6 relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </PageTransition>
      <Footer />
    </div>
  );
}

export default Layout;
