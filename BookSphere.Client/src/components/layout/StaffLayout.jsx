import { useState } from 'react';
import { Link, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

function StaffLayout({ children }) {
  const { isStaff, isAdmin, user } = useAuth();
  const location = useLocation();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Redirect non-staff users
  if (!isStaff) {
    return <Navigate to="/" />;
  }

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile sidebar toggle */}
      <div className="lg:hidden fixed top-0 left-0 z-20 p-4">
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-600"
        >
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d={isSidebarOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
            />
          </svg>
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-10 w-64 bg-blue-800 text-white transform ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 transition-transform duration-300 ease-in-out`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-center h-16 px-4 bg-blue-900">
            <Link to="/" className="text-xl font-bold">BookSphere Staff</Link>
          </div>

          <div className="flex-1 overflow-y-auto py-4">
            <div className="px-4 mb-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                  <span className="text-lg font-semibold">{user?.fullName?.charAt(0) || 'U'}</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{user?.fullName}</p>
                  <p className="text-xs text-blue-300">{user?.role}</p>
                </div>
              </div>
            </div>

            <nav className="space-y-1 px-2">
              <Link
                to="/staff"
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  isActive('/staff') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                }`}
              >
                <svg
                  className="mr-3 h-6 w-6 text-blue-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                  />
                </svg>
                Dashboard
              </Link>

              <Link
                to="/staff/orders"
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  isActive('/staff/orders') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                }`}
              >
                <svg
                  className="mr-3 h-6 w-6 text-blue-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                  />
                </svg>
                Orders
              </Link>

              <Link
                to="/staff/verify"
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                  isActive('/staff/verify') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                }`}
              >
                <svg
                  className="mr-3 h-6 w-6 text-blue-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Verify Orders
              </Link>

              {isAdmin && (
                <>
                  <div className="pt-4 pb-2">
                    <div className="px-2">
                      <p className="text-xs font-semibold text-blue-400 uppercase tracking-wider">
                        Admin
                      </p>
                    </div>
                  </div>

                  <Link
                    to="/staff/books"
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      isActive('/staff/books') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                    }`}
                  >
                    <svg
                      className="mr-3 h-6 w-6 text-blue-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                    Manage Books
                  </Link>

                  <Link
                    to="/staff/inventory"
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      isActive('/staff/inventory') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                    }`}
                  >
                    <svg
                      className="mr-3 h-6 w-6 text-blue-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    Inventory
                  </Link>

                  <Link
                    to="/staff/announcements"
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      isActive('/staff/announcements') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                    }`}
                  >
                    <svg
                      className="mr-3 h-6 w-6 text-blue-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
                      />
                    </svg>
                    Announcements
                  </Link>

                  <Link
                    to="/staff/users"
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      isActive('/staff/users') ? 'bg-blue-900 text-white' : 'text-blue-100 hover:bg-blue-700'
                    }`}
                  >
                    <svg
                      className="mr-3 h-6 w-6 text-blue-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                    Users
                  </Link>
                </>
              )}
            </nav>
          </div>

          <div className="p-4 border-t border-blue-700">
            <Link
              to="/"
              className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-blue-100 hover:bg-blue-700"
            >
              <svg
                className="mr-3 h-6 w-6 text-blue-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Back to Store
            </Link>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        <main className="py-6 px-4 sm:px-6 lg:px-8">
          {children}
        </main>
      </div>

      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-0 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
}

export default StaffLayout;
