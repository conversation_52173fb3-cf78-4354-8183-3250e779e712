import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

const AddToCartAnimation = ({ bookCoverUrl, bookTitle, startPosition, endPosition, onAnimationComplete }) => {
  const [isAnimating, setIsAnimating] = useState(true);
  const [position, setPosition] = useState(startPosition);
  const [scale, setScale] = useState(1);
  const [opacity, setOpacity] = useState(1);
  const [rotation, setRotation] = useState(0);
  const animationRef = useRef(null);
  
  useEffect(() => {
    // Get the start and end positions
    const startX = startPosition.x;
    const startY = startPosition.y;
    const endX = endPosition.x;
    const endY = endPosition.y;
    
    // Calculate the distance
    const distanceX = endX - startX;
    const distanceY = endY - startY;
    
    // Calculate the midpoint with an arc
    const midX = startX + distanceX / 2;
    const midY = startY + distanceY / 2 - 100; // Arc upward
    
    // Animation duration in ms
    const duration = 800;
    const startTime = performance.now();
    
    const animate = (currentTime) => {
      const elapsedTime = currentTime - startTime;
      const progress = Math.min(elapsedTime / duration, 1);
      
      // Bezier curve animation for smoother movement
      const bezierX = calculateBezier(startX, midX, endX, progress);
      const bezierY = calculateBezier(startY, midY, endY, progress);
      
      // Update position, scale, opacity, and rotation
      setPosition({ x: bezierX, y: bezierY });
      setScale(1 - progress * 0.7);
      setOpacity(1 - progress * 0.7);
      setRotation(progress * 720); // 2 full rotations
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
        if (onAnimationComplete) {
          onAnimationComplete();
        }
      }
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [startPosition, endPosition, onAnimationComplete]);
  
  // Bezier curve calculation for smooth animation
  const calculateBezier = (p0, p1, p2, t) => {
    return Math.pow(1 - t, 2) * p0 + 2 * (1 - t) * t * p1 + Math.pow(t, 2) * p2;
  };
  
  if (!isAnimating) {
    return null;
  }
  
  return createPortal(
    <div
      className="fixed z-50 pointer-events-none"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotation}deg)`,
        opacity: opacity,
        transition: 'transform 0.1s ease-out, opacity 0.1s ease-out',
      }}
    >
      <div className="w-16 h-16 rounded-md overflow-hidden shadow-lg">
        {bookCoverUrl ? (
          <img
            src={bookCoverUrl}
            alt={bookTitle}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white text-xl font-bold">
            {bookTitle.charAt(0)}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default AddToCartAnimation;
