import { useState } from 'react';

const AnimatedButton = ({ 
  children, 
  onClick, 
  className = '', 
  type = 'button',
  variant = 'primary',
  disabled = false,
  fullWidth = false,
  icon = null,
  loading = false
}) => {
  const [isPressed, setIsPressed] = useState(false);
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 hover:bg-blue-700 text-white shadow-blue-300';
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 text-white shadow-gray-300';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 text-white shadow-green-300';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white shadow-red-300';
      case 'warning':
        return 'bg-yellow-500 hover:bg-yellow-600 text-white shadow-yellow-300';
      case 'info':
        return 'bg-indigo-600 hover:bg-indigo-700 text-white shadow-indigo-300';
      case 'light':
        return 'bg-white hover:bg-gray-100 text-gray-800 border border-gray-300 shadow-gray-200';
      case 'dark':
        return 'bg-gray-800 hover:bg-gray-900 text-white shadow-gray-400';
      case 'outline':
        return 'bg-transparent hover:bg-blue-50 text-blue-600 border border-blue-600';
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white shadow-blue-300';
    }
  };
  
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        relative overflow-hidden rounded-md px-4 py-2 font-medium transition-all duration-300
        transform ${isPressed ? 'scale-95' : 'scale-100'}
        ${fullWidth ? 'w-full' : ''}
        ${getVariantClasses()}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg active:shadow-inner'}
        ${className}
      `}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
    >
      <span className="relative z-10 flex items-center justify-center">
        {loading && (
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
        
        {icon && !loading && (
          <span className="mr-2">{icon}</span>
        )}
        
        {children}
      </span>
      
      {/* Ripple effect */}
      <span className={`absolute inset-0 overflow-hidden rounded-md ${disabled ? 'hidden' : ''}`}>
        <span className="absolute inset-0 transform -translate-x-full hover:translate-x-0 transition-transform duration-700 ease-in-out bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></span>
      </span>
    </button>
  );
};

export default AnimatedButton;
