import { useState, useRef, useEffect } from 'react';

const Book3DCover = ({ coverImageUrl, title, author }) => {
  const [rotation, setRotation] = useState({ x: 0, y: 20 });
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(true);
  const bookRef = useRef(null);
  
  // Auto-animation effect
  useEffect(() => {
    if (!isHovered && isAnimating) {
      const interval = setInterval(() => {
        setRotation(prev => ({
          x: prev.x,
          y: Math.sin(Date.now() / 1000) * 10 + 10
        }));
      }, 50);
      
      return () => clearInterval(interval);
    }
  }, [isHovered, isAnimating]);
  
  const handleMouseMove = (e) => {
    if (!bookRef.current || !isHovered) return;
    
    const book = bookRef.current;
    const rect = book.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateY = ((x - centerX) / centerX) * 25;
    const rotateX = ((centerY - y) / centerY) * 10;
    
    setRotation({ x: rotateX, y: rotateY });
  };
  
  const handleMouseEnter = () => {
    setIsHovered(true);
    setIsAnimating(false);
  };
  
  const handleMouseLeave = () => {
    setIsHovered(false);
    setRotation({ x: 0, y: 20 });
    setTimeout(() => {
      setIsAnimating(true);
    }, 500);
  };
  
  // First letter of title for placeholder
  const firstLetter = title ? title.charAt(0) : 'B';
  
  return (
    <div 
      ref={bookRef}
      className="relative perspective-1000 w-full h-full"
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div 
        className="relative preserve-3d transition-all duration-300 w-full h-full"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Front cover */}
        <div 
          className="absolute inset-0 backface-hidden rounded-md overflow-hidden shadow-xl"
          style={{ transform: 'translateZ(10px)' }}
        >
          {coverImageUrl ? (
            <img 
              src={coverImageUrl} 
              alt={title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-indigo-600 text-white">
              <span className="text-6xl font-bold">{firstLetter}</span>
            </div>
          )}
        </div>
        
        {/* Book spine */}
        <div 
          className="absolute h-full w-20 bg-gradient-to-r from-gray-800 to-gray-700 backface-hidden"
          style={{ 
            transform: 'rotateY(-90deg) translateZ(10px) translateX(-10px)',
            transformOrigin: 'left center'
          }}
        >
          <div className="w-full h-full flex flex-col items-center justify-center p-2 text-white transform rotate-180" style={{ writingMode: 'vertical-rl' }}>
            <span className="text-xs font-bold truncate max-w-full">{title}</span>
            <span className="text-xs mt-2 opacity-80 truncate max-w-full">{author}</span>
          </div>
        </div>
        
        {/* Book back */}
        <div 
          className="absolute inset-0 backface-hidden bg-gray-200 rounded-md"
          style={{ transform: 'rotateY(180deg) translateZ(10px)' }}
        >
          <div className="w-full h-full p-4 flex flex-col justify-between">
            <div className="text-center">
              <h3 className="text-sm font-bold text-gray-700">{title}</h3>
              <p className="text-xs text-gray-600 mt-1">by {author}</p>
            </div>
            
            <div className="border-t border-gray-300 pt-2">
              <p className="text-xs text-gray-500 italic text-center">
                "A book is a dream that you hold in your hand."
              </p>
              <p className="text-xs text-gray-500 text-right mt-1">
                — Neil Gaiman
              </p>
            </div>
          </div>
        </div>
        
        {/* Book bottom edge */}
        <div 
          className="absolute w-full h-20 bg-gray-100 backface-hidden"
          style={{ 
            transform: 'rotateX(-90deg) translateZ(10px) translateY(-10px)',
            transformOrigin: 'bottom center'
          }}
        />
        
        {/* Book top edge */}
        <div 
          className="absolute w-full h-20 bg-gray-300 backface-hidden"
          style={{ 
            transform: 'rotateX(90deg) translateZ(10px) translateY(10px)',
            transformOrigin: 'top center'
          }}
        />
        
        {/* Book right edge */}
        <div 
          className="absolute h-full w-20 bg-gray-200 backface-hidden"
          style={{ 
            transform: 'rotateY(90deg) translateZ(calc(100% - 10px)) translateX(10px)',
            transformOrigin: 'right center'
          }}
        />
      </div>
      
      {/* Shadow */}
      <div 
        className="absolute bottom-0 left-0 right-0 mx-auto w-4/5 h-8 bg-black rounded-full filter blur-md transition-all duration-300"
        style={{
          opacity: 0.2,
          transform: `translateX(${rotation.y * 0.5}px) scale(${1 - Math.abs(rotation.y) / 100}, 1)`
        }}
      />
    </div>
  );
};

export default Book3DCover;
