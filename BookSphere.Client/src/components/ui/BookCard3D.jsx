import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';

const BookCard3D = ({ book }) => {
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  
  const handleMouseMove = (e) => {
    if (!cardRef.current) return;
    
    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateY = ((x - centerX) / centerX) * 10;
    const rotateX = ((centerY - y) / centerY) * 10;
    
    setRotation({ x: rotateX, y: rotateY });
  };
  
  const resetRotation = () => {
    setRotation({ x: 0, y: 0 });
    setIsHovered(false);
  };
  
  // Add shadow effect based on rotation
  const getShadowStyle = () => {
    const shadowX = rotation.y * 0.5;
    const shadowY = -rotation.x * 0.5;
    const shadowBlur = Math.max(Math.abs(rotation.x), Math.abs(rotation.y)) * 2;
    
    return {
      boxShadow: `
        ${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, 0.1),
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06)
      `
    };
  };
  
  return (
    <div 
      ref={cardRef}
      className="relative perspective-1000 transform transition-all duration-300 hover:z-10"
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={resetRotation}
    >
      <Link to={`/books/${book.id}`}>
        <div 
          className={`
            bg-white rounded-lg overflow-hidden shadow-lg transform transition-all duration-300
            ${isHovered ? 'scale-105' : 'scale-100'}
          `}
          style={{
            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
            ...getShadowStyle()
          }}
        >
          <div className="relative h-56 bg-blue-100">
            {book.coverImageUrl ? (
              <img 
                src={book.coverImageUrl} 
                alt={book.title}
                className="w-full h-full object-cover transform transition-transform duration-500"
                style={{
                  transform: isHovered ? 'scale(1.05)' : 'scale(1)'
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-indigo-500 text-white text-4xl font-bold">
                {book.title.charAt(0)}
              </div>
            )}
            
            {book.isOnSale && (
              <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full transform rotate-3 animate-pulse">
                Sale
              </div>
            )}
          </div>
          
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 line-clamp-1">{book.title}</h3>
            <p className="text-sm text-gray-600 mt-1">by {book.author}</p>
            
            <div className="mt-3 flex justify-between items-center">
              <div>
                {book.discountedPrice < book.price ? (
                  <div className="flex items-center">
                    <span className="text-gray-400 line-through text-sm mr-2">${book.price.toFixed(2)}</span>
                    <span className="text-red-600 font-bold">${book.discountedPrice.toFixed(2)}</span>
                  </div>
                ) : (
                  <span className="text-gray-800 font-bold">${book.price.toFixed(2)}</span>
                )}
              </div>
              
              <div className={`text-xs px-2 py-1 rounded-full ${
                book.stockQuantity > 10 
                  ? 'bg-green-100 text-green-800' 
                  : book.stockQuantity > 0 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-red-100 text-red-800'
              }`}>
                {book.stockQuantity > 10 
                  ? 'In Stock' 
                  : book.stockQuantity > 0 
                    ? `Only ${book.stockQuantity} left` 
                    : 'Out of Stock'}
              </div>
            </div>
          </div>
          
          {/* Shine effect */}
          <div 
            className={`absolute inset-0 bg-gradient-to-tr from-transparent via-white to-transparent opacity-0 pointer-events-none transition-opacity duration-1000 ${
              isHovered ? 'opacity-20' : ''
            }`}
            style={{
              transform: `translateX(${rotation.y * 10}px) translateY(${rotation.x * 10}px) rotateZ(25deg)`,
              backgroundSize: '200% 200%',
            }}
          />
        </div>
      </Link>
    </div>
  );
};

export default BookCard3D;
