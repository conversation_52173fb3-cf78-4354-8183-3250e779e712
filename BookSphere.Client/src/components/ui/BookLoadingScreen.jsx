import { useState, useEffect } from 'react';

const BookLoadingScreen = ({ loading, message, fullScreen = false }) => {
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingQuote, setLoadingQuote] = useState('');
  
  // Book quotes to display during loading
  const bookQuotes = [
    "A reader lives a thousand lives before he dies. The man who never reads lives only one. — <PERSON>",
    "Books are a uniquely portable magic. — <PERSON>",
    "I have always imagined that Paradise will be a kind of library. — <PERSON>",
    "The more that you read, the more things you will know. The more that you learn, the more places you'll go. — <PERSON><PERSON>",
    "A book is a dream that you hold in your hand. — <PERSON>",
    "Reading is to the mind what exercise is to the body. — <PERSON>",
    "Books are the mirrors of the soul. — Virginia Woolf",
    "A good book is an event in my life. — <PERSON><PERSON><PERSON>",
    "Reading brings us unknown friends. — <PERSON><PERSON>",
    "The world was hers for the reading. — <PERSON>"
  ];
  
  // Simulate loading progress
  useEffect(() => {
    if (loading) {
      setLoadingProgress(0);
      
      // Select a random quote
      const randomQuote = bookQuotes[Math.floor(Math.random() * bookQuotes.length)];
      setLoadingQuote(randomQuote);
      
      // Simulate progress
      const interval = setInterval(() => {
        setLoadingProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 300);
      
      return () => clearInterval(interval);
    }
  }, [loading]);
  
  if (!loading) return null;
  
  return (
    <div 
      className={`${fullScreen ? 'fixed inset-0 z-50' : 'relative'} bg-gradient-to-br from-blue-50 to-indigo-50 flex flex-col items-center justify-center`}
      style={{ minHeight: fullScreen ? '100vh' : '300px' }}
    >
      <div className="max-w-md w-full px-4">
        {/* Book animation */}
        <div className="relative w-32 h-40 mx-auto mb-8">
          {/* Book cover */}
          <div className="absolute inset-0 bg-blue-600 rounded-r-md rounded-b-md shadow-lg transform-gpu animate-pulse">
            <div className="absolute inset-2 bg-white rounded-r-sm rounded-b-sm flex items-center justify-center">
              <div className="text-blue-600 font-bold text-2xl">BS</div>
            </div>
          </div>
          
          {/* Book pages flipping animation */}
          <div className="absolute inset-y-0 right-0 w-1/2 bg-white rounded-r-md overflow-hidden">
            {[...Array(5)].map((_, index) => (
              <div 
                key={index}
                className="absolute inset-0 bg-gray-100 border-r border-gray-300 rounded-r-md"
                style={{
                  transformOrigin: 'left center',
                  animation: `bookPageFlip 1.5s ease-in-out infinite`,
                  animationDelay: `${index * 0.3}s`,
                }}
              ></div>
            ))}
          </div>
          
          {/* Book spine */}
          <div className="absolute inset-y-0 left-0 w-2 bg-blue-700 rounded-l-sm"></div>
        </div>
        
        {/* Loading message */}
        <div className="text-center mb-4">
          <h3 className="text-xl font-bold text-gray-800 mb-2">
            {message || 'Loading BookSphere'}
          </h3>
          <p className="text-gray-600 text-sm italic mb-4 h-12 flex items-center justify-center">
            {loadingQuote}
          </p>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div 
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${loadingProgress}%` }}
          ></div>
        </div>
        
        {/* Loading percentage */}
        <div className="text-center text-gray-600 text-sm">
          {Math.round(loadingProgress)}% Complete
        </div>
      </div>
      
      {/* Add page-turning animation keyframes */}
      <style jsx>{`
        @keyframes bookPageFlip {
          0% {
            transform: rotateY(0deg);
            z-index: 5;
          }
          20% {
            transform: rotateY(-30deg);
            z-index: 4;
          }
          40% {
            transform: rotateY(-70deg);
            z-index: 3;
          }
          60% {
            transform: rotateY(-120deg);
            z-index: 2;
          }
          80% {
            transform: rotateY(-170deg);
            z-index: 1;
          }
          100% {
            transform: rotateY(-180deg);
            z-index: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default BookLoadingScreen;
