import { useState, useRef, useEffect } from 'react';

const BookPreview = ({ book, previewPages = [] }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [isFlipping, setIsFlipping] = useState(false);
  const [flipDirection, setFlipDirection] = useState('next');
  const bookRef = useRef(null);
  
  // Default preview pages if none provided
  const pages = previewPages.length > 0 ? previewPages : [
    { type: 'cover', content: book.coverImageUrl },
    { type: 'text', content: book.description },
    { type: 'text', content: `Author: ${book.author}\nPublisher: ${book.publisher}\nISBN: ${book.isbn}` },
    { type: 'backcover', content: null }
  ];
  
  const totalPages = pages.length;
  
  const nextPage = () => {
    if (currentPage < totalPages - 1 && !isFlipping) {
      setFlipDirection('next');
      setIsFlipping(true);
      setTimeout(() => {
        setCurrentPage(prev => prev + 1);
        setTimeout(() => {
          setIsFlipping(false);
        }, 500);
      }, 500);
    }
  };
  
  const prevPage = () => {
    if (currentPage > 0 && !isFlipping) {
      setFlipDirection('prev');
      setIsFlipping(true);
      setTimeout(() => {
        setCurrentPage(prev => prev - 1);
        setTimeout(() => {
          setIsFlipping(false);
        }, 500);
      }, 500);
    }
  };
  
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowRight') {
        nextPage();
      } else if (e.key === 'ArrowLeft') {
        prevPage();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentPage, isFlipping]);
  
  // Render page content based on type
  const renderPageContent = (page) => {
    switch (page.type) {
      case 'cover':
        return (
          <div className="w-full h-full relative">
            {page.content ? (
              <img 
                src={page.content} 
                alt={book.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-indigo-600 text-white">
                <div className="text-center p-4">
                  <h2 className="text-2xl font-bold">{book.title}</h2>
                  <p className="mt-2">by {book.author}</p>
                </div>
              </div>
            )}
          </div>
        );
      case 'backcover':
        return (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300 p-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-700">{book.title}</h3>
              <p className="mt-2 text-gray-600">Thank you for previewing this book</p>
              <div className="mt-4 text-sm text-gray-500">
                <p>Published by {book.publisher}</p>
                <p>ISBN: {book.isbn}</p>
              </div>
            </div>
          </div>
        );
      case 'text':
      default:
        return (
          <div className="w-full h-full bg-white p-6 overflow-y-auto">
            <div className="prose max-w-none">
              {page.content.split('\n').map((paragraph, idx) => (
                <p key={idx} className="mb-4">{paragraph}</p>
              ))}
            </div>
          </div>
        );
    }
  };
  
  return (
    <div className="relative w-full max-w-2xl mx-auto">
      {/* Book container */}
      <div 
        ref={bookRef}
        className="relative perspective-1000 w-full"
        style={{ aspectRatio: '3/4' }}
      >
        {/* Book pages */}
        <div className="relative w-full h-full preserve-3d shadow-xl rounded-r-md">
          {/* Current page */}
          <div className="absolute inset-0 bg-white rounded-r-md overflow-hidden">
            {renderPageContent(pages[currentPage])}
          </div>
          
          {/* Flipping page */}
          {isFlipping && (
            <div 
              className={`absolute inset-0 bg-white rounded-r-md overflow-hidden backface-hidden transition-transform duration-1000 shadow-lg`}
              style={{
                transformOrigin: flipDirection === 'next' ? 'left center' : 'right center',
                transform: `rotateY(${flipDirection === 'next' ? '-180deg' : '180deg'})`,
              }}
            >
              {renderPageContent(pages[flipDirection === 'next' ? currentPage + 1 : currentPage - 1])}
            </div>
          )}
          
          {/* Page edge shadow */}
          <div 
            className="absolute inset-y-0 right-0 w-2 bg-gradient-to-l from-gray-200 to-transparent rounded-r-md"
          ></div>
        </div>
        
        {/* Book spine */}
        <div 
          className="absolute inset-y-0 left-0 w-4 bg-gradient-to-r from-gray-400 to-gray-300 rounded-l-md"
        ></div>
      </div>
      
      {/* Navigation controls */}
      <div className="flex justify-between mt-6">
        <button
          onClick={prevPage}
          disabled={currentPage === 0 || isFlipping}
          className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
        >
          Previous Page
        </button>
        
        <div className="text-center">
          <span className="text-gray-700">
            Page {currentPage + 1} of {totalPages}
          </span>
        </div>
        
        <button
          onClick={nextPage}
          disabled={currentPage === totalPages - 1 || isFlipping}
          className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
        >
          Next Page
        </button>
      </div>
    </div>
  );
};

export default BookPreview;
