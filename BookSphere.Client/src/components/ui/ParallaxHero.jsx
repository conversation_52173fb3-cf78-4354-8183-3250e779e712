import { useState, useEffect, useRef } from 'react';

const ParallaxHero = ({ title, subtitle, ctaText, ctaLink, backgroundImage }) => {
  const [offset, setOffset] = useState(0);
  const parallaxRef = useRef(null);
  const bookRefs = useRef([]);

  // Create refs for floating books
  useEffect(() => {
    bookRefs.current = Array(5).fill().map(() => ({ current: null }));
  }, []);
  
  // Handle parallax effect on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!parallaxRef.current) return;
      
      const scrollTop = window.pageYOffset;
      const parallaxElement = parallaxRef.current;
      const parallaxHeight = parallaxElement.offsetHeight;
      const windowHeight = window.innerHeight;
      
      // Only apply parallax when element is in view
      if (scrollTop < parallaxHeight + windowHeight) {
        setOffset(scrollTop * 0.5);
        
        // Animate floating books with different speeds
        bookRefs.current.forEach((bookRef, index) => {
          if (bookRef.current) {
            const speed = 0.2 + (index * 0.1);
            const rotation = scrollTop * 0.02 * (index % 2 === 0 ? 1 : -1);
            bookRef.current.style.transform = `translateY(${scrollTop * speed}px) rotate(${rotation}deg)`;
          }
        });
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  // Generate random positions for floating books
  const getRandomPosition = (index) => {
    const positions = [
      { top: '15%', left: '10%', size: '100px', delay: '0s' },
      { top: '60%', left: '15%', size: '80px', delay: '0.2s' },
      { top: '25%', left: '75%', size: '120px', delay: '0.4s' },
      { top: '70%', left: '80%', size: '90px', delay: '0.6s' },
      { top: '40%', left: '60%', size: '110px', delay: '0.8s' },
    ];
    
    return positions[index % positions.length];
  };
  
  // Book covers for floating books
  const bookCovers = [
    'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80',
    'https://images.unsplash.com/photo-1532012197267-da84d127e765?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80',
    'https://images.unsplash.com/photo-1629992101753-56d196c8aabb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80',
    'https://images.unsplash.com/photo-1543002588-bfa74002ed7e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80',
    'https://images.unsplash.com/photo-1621351183012-e2f9972dd9bf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80',
  ];
  
  return (
    <div 
      ref={parallaxRef}
      className="relative overflow-hidden"
      style={{ 
        height: '80vh',
        minHeight: '500px',
      }}
    >
      {/* Background image with parallax effect */}
      <div 
        className="absolute inset-0 bg-cover bg-center"
        style={{ 
          backgroundImage: `url(${backgroundImage || 'https://images.unsplash.com/photo-1507842217343-583bb7270b66?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2000&q=80'})`,
          transform: `translateY(${offset}px)`,
          height: 'calc(100% + 100px)',
        }}
      >
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-indigo-900/80"></div>
      </div>
      
      {/* Floating books */}
      {bookCovers.map((cover, index) => {
        const { top, left, size, delay } = getRandomPosition(index);
        return (
          <div
            key={index}
            ref={bookRefs.current[index]}
            className="absolute shadow-xl rounded-md overflow-hidden animate-float"
            style={{
              top,
              left,
              width: size,
              height: 'auto',
              aspectRatio: '2/3',
              animationDelay: delay,
              zIndex: 1,
            }}
          >
            <img 
              src={cover} 
              alt="Book cover" 
              className="w-full h-full object-cover"
            />
          </div>
        );
      })}
      
      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full text-center px-4">
        <h1 
          className="text-4xl md:text-6xl font-bold text-white mb-4 animate-typewriter"
          style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
        >
          {title || 'Discover Your Next Favorite Book'}
        </h1>
        
        <p 
          className="text-xl md:text-2xl text-white mb-8 max-w-2xl"
          style={{ textShadow: '0 1px 2px rgba(0,0,0,0.3)' }}
        >
          {subtitle || 'Explore our vast collection of books from bestselling authors and emerging talents.'}
        </p>
        
        <a 
          href={ctaLink || '/books'} 
          className="px-8 py-3 bg-white text-blue-900 font-bold rounded-full text-lg shadow-lg hover:shadow-xl transform transition-transform hover:scale-105 hover:bg-blue-50"
        >
          {ctaText || 'Browse Books'}
        </a>
      </div>
      
      {/* Bottom wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
          <path 
            fill="#f8fafc" 
            fillOpacity="1" 
            d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default ParallaxHero;
