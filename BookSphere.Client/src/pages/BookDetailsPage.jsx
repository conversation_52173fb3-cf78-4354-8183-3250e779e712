import { useState, useEffect, useRef } from 'react';
import { useParams, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import Book3DCover from '../components/ui/Book3DCover';
import AnimatedButton from '../components/ui/AnimatedButton';
import BookPreview from '../components/ui/BookPreview';
import AddToCartAnimation from '../components/ui/AddToCartAnimation';

function BookDetailsPage() {
  const { id } = useParams();
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const [addingToWhitelist, setAddingToWhitelist] = useState(false);
  const [actionSuccess, setActionSuccess] = useState(null);
  const [actionError, setActionError] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);
  const [animationConfig, setAnimationConfig] = useState(null);
  const bookCoverRef = useRef(null);
  const cartButtonRef = useRef(null);

  useEffect(() => {
    const fetchBookDetails = async () => {
      try {
        setLoading(true);
        const bookData = await api.books.getById(id);
        setBook(bookData);
      } catch (err) {
        setError('Failed to fetch book details. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    const fetchReviews = async () => {
      try {
        setReviewsLoading(true);
        const reviewsData = await api.reviews.getByBook(id);
        setReviews(reviewsData);
      } catch (err) {
        console.error('Failed to fetch reviews:', err);
      } finally {
        setReviewsLoading(false);
      }
    };

    fetchBookDetails();
    fetchReviews();

    // Check if we have a success message from review submission
    if (location.state?.reviewSuccess) {
      setActionSuccess('Your review has been submitted successfully!');

      // Clear the success message after 5 seconds
      const timer = setTimeout(() => {
        setActionSuccess(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [id, location.state]);

  const handleAddToCart = async () => {
    if (!isAuthenticated) return;

    try {
      setAddingToCart(true);
      setActionError(null);

      // Start animation before API call
      if (bookCoverRef.current && cartButtonRef.current) {
        const bookRect = bookCoverRef.current.getBoundingClientRect();
        const cartRect = cartButtonRef.current.getBoundingClientRect();

        setAnimationConfig({
          startPosition: {
            x: bookRect.left + bookRect.width / 2,
            y: bookRect.top + bookRect.height / 2
          },
          endPosition: {
            x: cartRect.left + cartRect.width / 2,
            y: cartRect.top + cartRect.height / 2
          }
        });

        setShowAnimation(true);

        // Wait for animation to start before API call
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      await api.cart.addItem({
        bookId: id,
        quantity: quantity
      });

      setActionSuccess('Book added to cart successfully!');

      // Clear the success message after 5 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 5000);
    } catch (err) {
      setActionError('Failed to add book to cart. Please try again.');
      console.error(err);
    } finally {
      // End animation after a delay to ensure it completes
      setTimeout(() => {
        setShowAnimation(false);
        setAddingToCart(false);
      }, 800);
    }
  };

  const handleAddToWhitelist = async () => {
    if (!isAuthenticated) return;

    try {
      setAddingToWhitelist(true);
      setActionError(null);

      await api.whitelist.addItem({
        bookId: id
      });

      setActionSuccess('Book added to whitelist successfully!');

      // Clear the success message after 5 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 5000);
    } catch (err) {
      setActionError('Failed to add book to whitelist. Please try again.');
      console.error(err);
    } finally {
      setAddingToWhitelist(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 my-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!book) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">Book not found.</p>
      </div>
    );
  }

  return (
    <div className="py-8">
      {/* Book Preview Modal */}
      {showPreview && book && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="absolute top-0 right-0 pt-4 pr-4 z-10">
              <button
                onClick={() => setShowPreview(false)}
                className="bg-white rounded-full p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-6 pt-12">
              <BookPreview book={book} />
            </div>
          </div>
        </div>
      )}

      {/* Add to Cart Animation */}
      {showAnimation && animationConfig && book && (
        <AddToCartAnimation
          bookCoverUrl={book.coverImageUrl}
          bookTitle={book.title}
          startPosition={animationConfig.startPosition}
          endPosition={animationConfig.endPosition}
          onAnimationComplete={() => setShowAnimation(false)}
        />
      )}
      {actionSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{actionSuccess}</p>
            </div>
          </div>
        </div>
      )}

      {actionError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{actionError}</p>
            </div>
          </div>
        </div>
      )}

      <div className="mb-6">
        <Link to="/books" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Books
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="md:flex">
          <div className="md:flex-shrink-0 md:w-1/3 bg-gradient-to-br from-blue-50 to-indigo-50 p-8">
            <div className="w-full h-80 md:h-96 relative animate-float" ref={bookCoverRef}>
              <Book3DCover
                coverImageUrl={book.coverImageUrl}
                title={book.title}
                author={book.author}
              />
            </div>

            <div className="mt-6 text-center">
              <button
                onClick={() => setShowPreview(true)}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md hover:shadow-lg transition-all duration-300"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Preview Book
              </button>
            </div>
          </div>

          <div className="p-6 md:w-2/3">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{book.title}</h1>
                <p className="text-lg text-gray-600 mb-4">by {book.author}</p>
              </div>

              {book.isOnSale && (
                <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  On Sale
                </div>
              )}
            </div>

            <div className="flex items-center mb-4">
              <div className="flex items-center mr-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className={`w-5 h-5 ${
                      star <= Math.round(book.averageRating)
                        ? 'text-yellow-400'
                        : 'text-gray-300'
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-gray-600 text-sm">
                {book.averageRating.toFixed(1)} ({book.reviewCount} reviews)
              </span>
            </div>

            <div className="mb-6">
              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">Price:</span>
                {book.discountedPrice < book.price ? (
                  <div className="flex items-center">
                    <span className="text-gray-400 line-through mr-2">${book.price.toFixed(2)}</span>
                    <span className="text-red-600 font-bold text-xl">${book.discountedPrice.toFixed(2)}</span>
                    <span className="ml-2 bg-red-100 text-red-800 text-xs font-semibold px-2 py-0.5 rounded">
                      {Math.round(((book.price - book.discountedPrice) / book.price) * 100)}% OFF
                    </span>
                  </div>
                ) : (
                  <span className="text-gray-900 font-bold text-xl">${book.price.toFixed(2)}</span>
                )}
              </div>

              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">Availability:</span>
                {book.stockQuantity > 0 ? (
                  <span className="text-green-600">In Stock ({book.stockQuantity} available)</span>
                ) : (
                  <span className="text-red-600">Out of Stock</span>
                )}
              </div>

              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">ISBN:</span>
                <span className="text-gray-600">{book.isbn}</span>
              </div>

              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">Publisher:</span>
                <span className="text-gray-600">{book.publisher}</span>
              </div>

              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">Publication Date:</span>
                <span className="text-gray-600">{new Date(book.publicationDate).toLocaleDateString()}</span>
              </div>

              <div className="flex items-center mb-2">
                <span className="text-gray-700 font-semibold mr-2">Format:</span>
                <span className="text-gray-600">{book.format}</span>
              </div>

              <div className="flex items-center">
                <span className="text-gray-700 font-semibold mr-2">Language:</span>
                <span className="text-gray-600">{book.language}</span>
              </div>
            </div>

            {isAuthenticated && (
              <div className="mt-6 mb-4">
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 border border-gray-300 rounded-l-md bg-gray-50 hover:bg-gray-100"
                  >
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"></path>
                    </svg>
                  </button>
                  <input
                    type="number"
                    id="quantity"
                    name="quantity"
                    min="1"
                    max={book.stockQuantity}
                    value={quantity}
                    onChange={(e) => setQuantity(Math.min(book.stockQuantity, Math.max(1, parseInt(e.target.value) || 1)))}
                    className="p-2 w-16 text-center border-t border-b border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setQuantity(Math.min(book.stockQuantity, quantity + 1))}
                    className="p-2 border border-gray-300 rounded-r-md bg-gray-50 hover:bg-gray-100"
                  >
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                  </button>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 mt-6">
              {isAuthenticated ? (
                <>
                  <AnimatedButton
                    onClick={handleAddToCart}
                    disabled={book.stockQuantity === 0 || addingToCart}
                    variant="primary"
                    loading={addingToCart}
                    fullWidth
                    className="py-2 px-4 rounded-md font-medium"
                    icon={<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>}
                    ref={cartButtonRef}
                  >
                    Add to Cart
                  </AnimatedButton>

                  <AnimatedButton
                    onClick={handleAddToWhitelist}
                    disabled={addingToWhitelist}
                    variant="outline"
                    loading={addingToWhitelist}
                    fullWidth
                    className="py-2 px-4 rounded-md font-medium"
                    icon={<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>}
                  >
                    Add to Wishlist
                  </AnimatedButton>
                </>
              ) : (
                <Link
                  to="/login"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium text-center"
                >
                  Login to Purchase
                </Link>
              )}
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Description</h2>
          <p className="text-gray-700 whitespace-pre-line">{book.description}</p>
        </div>

        <div className="p-6 border-t border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Reviews</h2>

          {reviewsLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : reviews.length > 0 ? (
            <div className="space-y-6">
              {reviews.map((review) => (
                <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                  <div className="flex items-center mb-2">
                    <div className="flex items-center mr-3">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="font-semibold text-gray-900">{review.userName}</span>
                    <span className="mx-2 text-gray-300">•</span>
                    <span className="text-sm text-gray-500">
                      {new Date(review.reviewDate).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No reviews yet. Be the first to review this book!</p>
          )}

          {isAuthenticated && (
            <div className="mt-8">
              <Link
                to={`/books/${book.id}/review`}
                className="inline-flex items-center text-blue-600 hover:text-blue-800"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Write a Review
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default BookDetailsPage;
