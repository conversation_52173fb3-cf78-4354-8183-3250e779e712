import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';

function CartPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [processingAction, setProcessingAction] = useState(false);

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const cartData = await api.cart.get();
      setCart(cartData);
    } catch (err) {
      setError('Failed to fetch cart. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateQuantity = async (cartItemId, quantity) => {
    if (quantity < 1) return;
    
    try {
      setProcessingAction(true);
      await api.cart.updateItem({ cartItemId, quantity });
      await fetchCart();
    } catch (err) {
      setError('Failed to update quantity. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleRemoveItem = async (cartItemId) => {
    try {
      setProcessingAction(true);
      await api.cart.removeItem(cartItemId);
      await fetchCart();
    } catch (err) {
      setError('Failed to remove item. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleClearCart = async () => {
    if (!window.confirm('Are you sure you want to clear your cart?')) return;
    
    try {
      setProcessingAction(true);
      await api.cart.clear();
      await fetchCart();
    } catch (err) {
      setError('Failed to clear cart. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleCheckout = async () => {
    try {
      setProcessingAction(true);
      const isValid = await api.cart.validate();
      
      if (isValid) {
        navigate('/checkout');
      } else {
        setError('Some items in your cart are no longer available in the requested quantity.');
        await fetchCart();
      }
    } catch (err) {
      setError('Failed to proceed to checkout. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 my-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Your Cart</h1>
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-6">Looks like you haven't added any books to your cart yet.</p>
          <Link to="/books" className="inline-flex items-center justify-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Browse Books
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Your Cart</h1>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6">
          <div className="flow-root">
            <ul className="-my-6 divide-y divide-gray-200">
              {cart.items.map((item) => (
                <li key={item.id} className="py-6 flex">
                  <div className="flex-shrink-0 w-24 h-24 bg-gray-200 rounded-md overflow-hidden">
                    {item.book.coverImageUrl ? (
                      <img
                        src={item.book.coverImageUrl}
                        alt={item.book.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500 text-2xl font-bold">
                        {item.book.title.substring(0, 1)}
                      </div>
                    )}
                  </div>

                  <div className="ml-4 flex-1 flex flex-col">
                    <div>
                      <div className="flex justify-between text-base font-medium text-gray-900">
                        <h3>
                          <Link to={`/books/${item.book.id}`} className="hover:text-blue-600">
                            {item.book.title}
                          </Link>
                        </h3>
                        <p className="ml-4">
                          {item.discountedPrice < item.unitPrice ? (
                            <span className="flex flex-col items-end">
                              <span className="text-gray-400 line-through text-sm">${item.unitPrice.toFixed(2)}</span>
                              <span className="text-red-600">${item.discountedPrice.toFixed(2)}</span>
                            </span>
                          ) : (
                            <span>${item.unitPrice.toFixed(2)}</span>
                          )}
                        </p>
                      </div>
                      <p className="mt-1 text-sm text-gray-500">by {item.book.author}</p>
                    </div>
                    <div className="flex-1 flex items-end justify-between text-sm">
                      <div className="flex items-center">
                        <button
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                          disabled={processingAction || item.quantity <= 1}
                          className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"></path>
                          </svg>
                        </button>
                        <span className="mx-2 text-gray-700">{item.quantity}</span>
                        <button
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          disabled={processingAction}
                          className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                        </button>
                      </div>

                      <div className="flex">
                        <button
                          type="button"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={processingAction}
                          className="font-medium text-red-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 px-6 py-4 sm:px-6">
          <div className="flex justify-between text-base font-medium text-gray-900 mb-2">
            <p>Subtotal</p>
            <p>${cart.subTotal.toFixed(2)}</p>
          </div>
          
          {cart.discountAmount > 0 && (
            <div className="flex justify-between text-base font-medium text-red-600 mb-2">
              <p>Discount</p>
              <p>-${cart.discountAmount.toFixed(2)}</p>
            </div>
          )}
          
          {cart.qualifiesForBulkDiscount && (
            <div className="flex justify-between text-sm text-green-600 mb-2">
              <p>Bulk Discount (5% for 5+ books)</p>
              <p>Applied</p>
            </div>
          )}
          
          {cart.hasLoyaltyDiscount && (
            <div className="flex justify-between text-sm text-green-600 mb-2">
              <p>Loyalty Discount (10% after 10 orders)</p>
              <p>Applied</p>
            </div>
          )}
          
          <div className="flex justify-between text-lg font-bold text-gray-900 mb-4">
            <p>Total</p>
            <p>${cart.total.toFixed(2)}</p>
          </div>
          
          <div className="mt-6 flex justify-between">
            <button
              type="button"
              onClick={handleClearCart}
              disabled={processingAction}
              className="text-sm font-medium text-red-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Cart
            </button>
            
            <button
              type="button"
              onClick={handleCheckout}
              disabled={processingAction}
              className="flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {processingAction ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Checkout'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CartPage;
