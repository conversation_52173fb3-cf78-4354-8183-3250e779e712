import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';

function CheckoutPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [processingOrder, setProcessingOrder] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const cartData = await api.cart.get();
      
      // Validate cart before proceeding
      await api.cart.validate();
      
      setCart(cartData);
    } catch (err) {
      setError('There was an issue with your cart. Please review your items and try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceOrder = async () => {
    try {
      setProcessingOrder(true);
      setError(null);
      
      // Create order from cart
      const orderData = await api.orders.create({});
      
      setOrderSuccess(true);
      setOrderDetails(orderData);
    } catch (err) {
      setError('Failed to place your order. Please try again.');
      console.error(err);
    } finally {
      setProcessingOrder(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Checkout</h1>
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <Link to="/cart" className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            Return to Cart
          </Link>
        </div>
      </div>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Checkout</h1>
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-6">You need to add items to your cart before checking out.</p>
          <Link to="/books" className="inline-flex items-center justify-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Browse Books
          </Link>
        </div>
      </div>
    );
  }

  if (orderSuccess && orderDetails) {
    return (
      <div className="py-8">
        <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 text-green-600 mb-4">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Order Placed Successfully!</h1>
            <p className="text-gray-600">Thank you for your order. We've sent a confirmation email with your claim code.</p>
          </div>

          <div className="border-t border-b border-gray-200 py-4 mb-6">
            <div className="flex justify-between mb-2">
              <span className="font-medium text-gray-700">Order Number:</span>
              <span className="text-gray-900">{orderDetails.id.substring(0, 8).toUpperCase()}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium text-gray-700">Claim Code:</span>
              <span className="text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">{orderDetails.claimCode}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium text-gray-700">Date:</span>
              <span className="text-gray-900">{new Date(orderDetails.orderDate).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">Total:</span>
              <span className="text-gray-900 font-bold">${orderDetails.finalAmount.toFixed(2)}</span>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Order Items</h2>
            <ul className="divide-y divide-gray-200">
              {orderDetails.items.map((item) => (
                <li key={item.id} className="py-3 flex justify-between">
                  <div>
                    <p className="text-gray-900 font-medium">{item.book.title}</p>
                    <p className="text-gray-500 text-sm">Qty: {item.quantity}</p>
                  </div>
                  <p className="text-gray-900">${item.subTotal.toFixed(2)}</p>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-blue-50 p-4 rounded-md mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">How to pick up your order</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>Visit our store and present your membership ID and claim code to a staff member to complete your purchase.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-center space-x-4">
            <Link to="/orders" className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              View My Orders
            </Link>
            <Link to="/books" className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Checkout</h1>
      
      <div className="lg:grid lg:grid-cols-2 lg:gap-x-12 xl:gap-x-16">
        <div>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Order Summary</h2>
            
            <ul className="divide-y divide-gray-200">
              {cart.items.map((item) => (
                <li key={item.id} className="py-4 flex">
                  <div className="flex-shrink-0 w-16 h-16 bg-gray-200 rounded-md overflow-hidden">
                    {item.book.coverImageUrl ? (
                      <img
                        src={item.book.coverImageUrl}
                        alt={item.book.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500 text-xl font-bold">
                        {item.book.title.substring(0, 1)}
                      </div>
                    )}
                  </div>
                  <div className="ml-4 flex-1 flex flex-col">
                    <div>
                      <div className="flex justify-between text-base font-medium text-gray-900">
                        <h3>{item.book.title}</h3>
                        <p className="ml-4">${item.subtotal.toFixed(2)}</p>
                      </div>
                      <p className="mt-1 text-sm text-gray-500">Qty: {item.quantity}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
            
            <div className="border-t border-gray-200 pt-4 mt-4">
              <div className="flex justify-between text-base font-medium text-gray-900 mb-2">
                <p>Subtotal</p>
                <p>${cart.subTotal.toFixed(2)}</p>
              </div>
              
              {cart.discountAmount > 0 && (
                <div className="flex justify-between text-base font-medium text-red-600 mb-2">
                  <p>Discount</p>
                  <p>-${cart.discountAmount.toFixed(2)}</p>
                </div>
              )}
              
              {cart.qualifiesForBulkDiscount && (
                <div className="flex justify-between text-sm text-green-600 mb-2">
                  <p>Bulk Discount (5% for 5+ books)</p>
                  <p>Applied</p>
                </div>
              )}
              
              {cart.hasLoyaltyDiscount && (
                <div className="flex justify-between text-sm text-green-600 mb-2">
                  <p>Loyalty Discount (10% after 10 orders)</p>
                  <p>Applied</p>
                </div>
              )}
              
              <div className="flex justify-between text-lg font-bold text-gray-900 mt-2">
                <p>Total</p>
                <p>${cart.total.toFixed(2)}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Pickup Information</h2>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-gray-700">
                    After placing your order, you'll receive a confirmation email with a claim code. Visit our store and present your membership ID and claim code to complete your purchase.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-10 lg:mt-0">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Your Information</h2>
            
            <div className="grid grid-cols-1 gap-y-6">
              <div>
                <p className="block text-sm font-medium text-gray-700">Full Name</p>
                <p className="mt-1 text-gray-900">{user.fullName}</p>
              </div>
              
              <div>
                <p className="block text-sm font-medium text-gray-700">Email Address</p>
                <p className="mt-1 text-gray-900">{user.email}</p>
              </div>
              
              <div>
                <p className="block text-sm font-medium text-gray-700">Phone Number</p>
                <p className="mt-1 text-gray-900">{user.phone}</p>
              </div>
              
              <div>
                <p className="block text-sm font-medium text-gray-700">Address</p>
                <p className="mt-1 text-gray-900">{user.address}</p>
              </div>
              
              <div>
                <p className="block text-sm font-medium text-gray-700">Membership ID</p>
                <p className="mt-1 text-gray-900 font-mono bg-gray-100 inline-block px-2 py-1 rounded">{user.membershipId}</p>
              </div>
            </div>
            
            <div className="mt-8">
              <button
                type="button"
                onClick={handlePlaceOrder}
                disabled={processingOrder}
                className="w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {processingOrder ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing Order...
                  </>
                ) : (
                  'Place Order'
                )}
              </button>
              
              <div className="mt-4">
                <Link
                  to="/cart"
                  className="text-sm text-blue-600 hover:text-blue-500 flex justify-center items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                  </svg>
                  Return to Cart
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CheckoutPage;
