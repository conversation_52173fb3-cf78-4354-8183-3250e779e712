import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useState, useEffect } from 'react';
import ParallaxHero from '../components/ui/ParallaxHero';
import BookLoadingScreen from '../components/ui/BookLoadingScreen';
import BookCard3D from '../components/ui/BookCard3D';
import api from '../services/api';

function HomePage() {
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [featuredBooks, setFeaturedBooks] = useState([]);
  const [newArrivals, setNewArrivals] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch featured books
        const featuredResponse = await api.books.getAll(1, 4, { featured: true });
        setFeaturedBooks(featuredResponse.items);

        // Fetch new arrivals
        const newArrivalsResponse = await api.books.getAll(1, 4, { newArrivals: true });
        setNewArrivals(newArrivalsResponse.items);
      } catch (error) {
        console.error('Error fetching homepage data:', error);
      } finally {
        // Simulate a minimum loading time for the animation
        setTimeout(() => {
          setLoading(false);
        }, 1500);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <BookLoadingScreen loading={loading} message="Welcome to BookSphere" fullScreen={true} />;
  }

  return (
    <div className="pb-12">
      {/* Hero Section with Parallax */}
      <ParallaxHero
        title="Discover Your Next Favorite Book"
        subtitle="Explore our vast collection of books from bestselling authors and emerging talents."
        ctaText="Browse Books"
        ctaLink="/books"
      />

      {/* Featured Books Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Books</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredBooks.map((book, index) => (
              <div key={book.id} className="stagger-item" style={{ animationDelay: `${index * 0.1}s` }}>
                <BookCard3D book={book} />
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/books"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              View All Books
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section with Animation */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-blue-50 to-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Why Choose BookSphere?</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-lg hover-lift hover:shadow-xl transition-all duration-300">
              <div className="text-blue-600 text-4xl mb-6 animate-float">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-center">Wide Selection</h3>
              <p className="text-gray-600 text-center">Explore our vast collection of books across all genres, from bestsellers to rare finds.</p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg hover-lift hover:shadow-xl transition-all duration-300">
              <div className="text-blue-600 text-4xl mb-6 animate-float" style={{ animationDelay: '0.2s' }}>
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-center">Great Deals</h3>
              <p className="text-gray-600 text-center">Enjoy competitive prices, regular discounts, and special offers on your favorite titles.</p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-lg hover-lift hover:shadow-xl transition-all duration-300">
              <div className="text-blue-600 text-4xl mb-6 animate-float" style={{ animationDelay: '0.4s' }}>
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-center">Easy Ordering</h3>
              <p className="text-gray-600 text-center">Simple checkout process, fast delivery, and excellent customer service.</p>
            </div>
          </div>
        </div>
      </section>

      {/* New Arrivals Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">New Arrivals</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {newArrivals.map((book, index) => (
              <div key={book.id} className="stagger-item" style={{ animationDelay: `${index * 0.1}s` }}>
                <BookCard3D book={book} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Community of Book Lovers</h2>
          <p className="text-xl mb-8">Sign up today to receive personalized recommendations, exclusive offers, and updates on new releases.</p>

          {!isAuthenticated && (
            <Link
              to="/register"
              className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-full text-blue-700 bg-white hover:bg-blue-50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              Sign Up Now
            </Link>
          )}

          {isAuthenticated && (
            <Link
              to="/books"
              className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-full text-blue-700 bg-white hover:bg-blue-50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              Explore Books
            </Link>
          )}
        </div>
      </section>
    </div>
  );
}

export default HomePage;
