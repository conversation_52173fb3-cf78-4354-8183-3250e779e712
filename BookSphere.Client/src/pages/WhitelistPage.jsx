import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import api from '../services/api';

function WhitelistPage() {
  const [whitelist, setWhitelist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [processingAction, setProcessingAction] = useState(false);
  const [actionItemId, setActionItemId] = useState(null);

  useEffect(() => {
    fetchWhitelist();
  }, []);

  const fetchWhitelist = async () => {
    try {
      setLoading(true);
      const whitelistData = await api.whitelist.get();
      setWhitelist(whitelistData);
    } catch (err) {
      setError('Failed to fetch whitelist. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromWhitelist = async (bookId) => {
    try {
      setProcessingAction(true);
      setActionItemId(bookId);
      await api.whitelist.removeItem(bookId);
      await fetchWhitelist();
    } catch (err) {
      setError('Failed to remove item from whitelist. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
      setActionItemId(null);
    }
  };

  const handleMoveToCart = async (bookId) => {
    try {
      setProcessingAction(true);
      setActionItemId(bookId);
      await api.whitelist.moveToCart(bookId, 1);
      await fetchWhitelist();
    } catch (err) {
      setError('Failed to move item to cart. Please try again.');
      console.error(err);
    } finally {
      setProcessingAction(false);
      setActionItemId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 my-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (whitelist.length === 0) {
    return (
      <div className="py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">My Whitelist</h1>
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Your whitelist is empty</h2>
          <p className="text-gray-500 mb-6">Save books you're interested in for later.</p>
          <Link to="/books" className="inline-flex items-center justify-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Browse Books
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">My Whitelist</h1>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {whitelist.map((item) => (
          <div key={item.book.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="relative pb-[140%] bg-gray-200">
              {item.book.coverImageUrl ? (
                <img 
                  src={item.book.coverImageUrl} 
                  alt={item.book.title} 
                  className="absolute inset-0 w-full h-full object-cover"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center bg-blue-100 text-blue-500 text-4xl font-bold">
                  {item.book.title.substring(0, 1)}
                </div>
              )}
              
              {item.book.discountPercentage > 0 && (
                <div className="absolute top-0 right-0 bg-red-500 text-white px-2 py-1 text-xs font-bold">
                  {item.book.discountPercentage}% OFF
                </div>
              )}
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">{item.book.title}</h3>
              <p className="text-sm text-gray-600 mb-2">by {item.book.author}</p>
              
              <div className="flex items-center mb-3">
                {item.book.discountedPrice < item.book.price ? (
                  <>
                    <span className="text-gray-400 line-through mr-2">${item.book.price.toFixed(2)}</span>
                    <span className="text-red-600 font-bold">${item.book.discountedPrice.toFixed(2)}</span>
                  </>
                ) : (
                  <span className="text-gray-900 font-bold">${item.book.price.toFixed(2)}</span>
                )}
              </div>
              
              <div className="flex flex-col space-y-2">
                <Link 
                  to={`/books/${item.book.id}`} 
                  className="block w-full text-center py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-300"
                >
                  View Details
                </Link>
                
                <button
                  onClick={() => handleMoveToCart(item.book.id)}
                  disabled={processingAction && actionItemId === item.book.id}
                  className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processingAction && actionItemId === item.book.id ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Add to Cart'
                  )}
                </button>
                
                <button
                  onClick={() => handleRemoveFromWhitelist(item.book.id)}
                  disabled={processingAction && actionItemId === item.book.id}
                  className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processingAction && actionItemId === item.book.id ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Remove'
                  )}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default WhitelistPage;
