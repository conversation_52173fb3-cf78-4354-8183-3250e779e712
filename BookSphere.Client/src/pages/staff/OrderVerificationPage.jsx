import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';

function OrderVerificationPage() {
  const navigate = useNavigate();
  const [claimCode, setClaimCode] = useState('');
  const [membershipId, setMembershipId] = useState('');
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!claimCode.trim()) {
      setError('Please enter a claim code');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setOrder(null);
      
      const orderData = await api.staff.getOrderByClaimCode(claimCode.trim());
      setOrder(orderData);
    } catch (err) {
      setError('Order not found. Please check the claim code and try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async () => {
    if (!order) return;
    
    if (!membershipId.trim()) {
      setError('Please enter the customer\'s membership ID');
      return;
    }
    
    try {
      setVerifying(true);
      setError(null);
      setSuccess(null);
      
      await api.staff.verifyOrder(order.id, membershipId.trim());
      
      setSuccess('Order has been successfully verified and marked as completed!');
      setOrder(null);
      setClaimCode('');
      setMembershipId('');
      
      // Redirect to the order details after a short delay
      setTimeout(() => {
        navigate(`/staff/orders/${order.id}`);
      }, 2000);
    } catch (err) {
      setError('Failed to verify order. Please check the membership ID and try again.');
      console.error(err);
    } finally {
      setVerifying(false);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Verify Customer Order</h1>
        <p className="mt-1 text-sm text-gray-600">
          Enter the customer's claim code to verify and complete their order.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <form onSubmit={handleSearch} className="space-y-6">
            <div>
              <label htmlFor="claimCode" className="block text-sm font-medium text-gray-700">
                Order Claim Code
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <input
                  type="text"
                  name="claimCode"
                  id="claimCode"
                  value={claimCode}
                  onChange={(e) => setClaimCode(e.target.value)}
                  className="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter claim code"
                />
                <button
                  type="submit"
                  disabled={loading}
                  className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </>
                  ) : (
                    'Search'
                  )}
                </button>
              </div>
            </div>
          </form>

          {order && (
            <div className="mt-8">
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900">Order Details</h3>
                
                <div className="mt-4 bg-gray-50 p-4 rounded-md">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Order ID</p>
                      <p className="mt-1 text-sm text-gray-900">{order.id.substring(0, 8).toUpperCase()}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Order Date</p>
                      <p className="mt-1 text-sm text-gray-900">{new Date(order.orderDate).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Customer</p>
                      <p className="mt-1 text-sm text-gray-900">{order.userName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status</p>
                      <p className={`mt-1 text-sm font-semibold ${
                        order.status === 'Pending' ? 'text-yellow-600' : 
                        order.status === 'Completed' ? 'text-green-600' : 
                        order.status === 'Cancelled' ? 'text-red-600' : 
                        'text-gray-600'
                      }`}>
                        {order.status}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Amount</p>
                      <p className="mt-1 text-sm text-gray-900">${order.finalAmount.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Items</p>
                      <p className="mt-1 text-sm text-gray-900">{order.items.length} items</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-900">Order Items</h4>
                  <ul className="mt-2 divide-y divide-gray-200 border-t border-b border-gray-200">
                    {order.items.map((item) => (
                      <li key={item.id} className="py-3 flex items-center">
                        <div className="flex-shrink-0 w-16 h-16 bg-gray-200 rounded-md overflow-hidden">
                          {item.book.coverImageUrl ? (
                            <img
                              src={item.book.coverImageUrl}
                              alt={item.book.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500 text-xl font-bold">
                              {item.book.title.substring(0, 1)}
                            </div>
                          )}
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex justify-between">
                            <h5 className="text-sm font-medium text-gray-900">{item.book.title}</h5>
                            <p className="text-sm font-medium text-gray-900">${item.subTotal.toFixed(2)}</p>
                          </div>
                          <p className="mt-1 text-sm text-gray-500">Qty: {item.quantity}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>

                {order.status === 'Pending' ? (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900">Verify Order</h4>
                    <div className="mt-2">
                      <label htmlFor="membershipId" className="block text-sm font-medium text-gray-700">
                        Customer Membership ID
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="membershipId"
                          id="membershipId"
                          value={membershipId}
                          onChange={(e) => setMembershipId(e.target.value)}
                          className="block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Enter customer's membership ID"
                        />
                      </div>
                      <div className="mt-4">
                        <button
                          type="button"
                          onClick={handleVerify}
                          disabled={verifying || !membershipId.trim()}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {verifying ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Verifying...
                            </>
                          ) : (
                            'Verify and Complete Order'
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mt-6 bg-yellow-50 p-4 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800">
                          This order is already {order.status.toLowerCase()}
                        </h3>
                        <div className="mt-2 text-sm text-yellow-700">
                          <p>
                            {order.status === 'Completed'
                              ? 'This order has already been verified and completed.'
                              : 'This order has been cancelled and cannot be verified.'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default OrderVerificationPage;
