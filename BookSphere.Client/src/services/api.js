// Base API URL - update this to match your backend API URL
const API_URL = 'http://localhost:5000/api';

// Helper function to handle API responses
const handleResponse = async (response) => {
  // For 204 No Content responses
  if (response.status === 204) {
    return null;
  }

  // For responses with content
  if (response.headers.get('content-type')?.includes('application/json')) {
    const data = await response.json();

    if (!response.ok) {
      // Extract error message with fallbacks for different API response formats
      const errorMessage = data.message || data.title || data.error || response.statusText;
      console.error('API Error:', errorMessage, data);
      return Promise.reject(errorMessage);
    }

    return data;
  }

  // For non-JSON responses
  if (!response.ok) {
    console.error('API Error (non-JSON):', response.statusText);
    return Promise.reject(`Server error: ${response.status} ${response.statusText}`);
  }

  return null;
};

// Get auth token from local storage
const getToken = () => {
  return localStorage.getItem('token');
};

// API request with authorization header
const authRequest = async (url, options = {}) => {
  const token = getToken();

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const config = {
    ...options,
    headers,
  };

  try {
    const response = await fetch(`${API_URL}${url}`, config);
    return await handleResponse(response);
  } catch (error) {
    // Handle network errors (like CORS, server down, etc.)
    console.error(`API request failed: ${url}`, error);

    // Provide a more user-friendly error message
    if (error instanceof TypeError && error.message.includes('NetworkError')) {
      throw new Error('Network error. Please check your internet connection.');
    } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('Unable to connect to the server. Please try again later.');
    }

    throw error;
  }
};

// API service object
const api = {
  // Auth endpoints
  auth: {
    login: (credentials) => authRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }),
    register: (userData) => authRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    }),
    logout: () => authRequest('/auth/logout', {
      method: 'POST',
    }),
    validateToken: () => authRequest('/auth/validate-token'),
    updateProfile: (profileData) => authRequest('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    }),
    changePassword: (passwordData) => authRequest('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(passwordData),
    }),
  },

  // Books endpoints
  books: {
    getAll: (page = 1, pageSize = 10, filters = {}) => {
      const queryParams = new URLSearchParams({
        pageNumber: page,
        pageSize,
        ...filters,
      });

      return authRequest(`/book?${queryParams}`);
    },
    getById: (id) => authRequest(`/book/${id}`),
    search: (query, page = 1, pageSize = 10) => {
      const queryParams = new URLSearchParams({
        query,
        pageNumber: page,
        pageSize,
      });

      return authRequest(`/book/search?${queryParams}`);
    },
    getByCategory: (categoryId, page = 1, pageSize = 10) => {
      const queryParams = new URLSearchParams({
        pageNumber: page,
        pageSize,
      });

      return authRequest(`/book/category/${categoryId}?${queryParams}`);
    },
    getNewReleases: (count = 10) => authRequest(`/book/new-releases?count=${count}`),
    getBestsellers: (count = 10) => authRequest(`/book/bestsellers?count=${count}`),
  },

  // Cart endpoints
  cart: {
    get: () => authRequest('/cart'),
    addItem: (item) => authRequest('/cart/add', {
      method: 'POST',
      body: JSON.stringify(item),
    }),
    updateItem: (item) => authRequest('/cart/update', {
      method: 'PUT',
      body: JSON.stringify(item),
    }),
    removeItem: (itemId) => authRequest(`/cart/remove/${itemId}`, {
      method: 'DELETE',
    }),
    clear: () => authRequest('/cart/clear', {
      method: 'DELETE',
    }),
    validate: () => authRequest('/cart/validate'),
  },

  // Whitelist endpoints
  whitelist: {
    get: () => authRequest('/whitelist'),
    addItem: (item) => authRequest('/whitelist/add', {
      method: 'POST',
      body: JSON.stringify(item),
    }),
    removeItem: (bookId) => authRequest(`/whitelist/remove/${bookId}`, {
      method: 'DELETE',
    }),
    moveToCart: (bookId, quantity = 1) => authRequest(`/whitelist/move-to-cart/${bookId}`, {
      method: 'POST',
      body: JSON.stringify({ quantity }),
    }),
    clear: () => authRequest('/whitelist/clear', {
      method: 'DELETE',
    }),
  },

  // Order endpoints
  orders: {
    create: (orderData = {}) => authRequest('/order', {
      method: 'POST',
      body: JSON.stringify(orderData),
    }),
    getById: (id) => authRequest(`/order/${id}`),
    getUserOrders: () => authRequest('/order/user'),
    cancel: (id) => authRequest(`/order/${id}/cancel`, {
      method: 'POST',
    }),
    getClaimCode: (id) => authRequest(`/order/${id}/claim-code`),
  },

  // Review endpoints
  reviews: {
    getByBook: (bookId) => authRequest(`/review/book/${bookId}`),
    getUserReviews: () => authRequest('/review/user'),
    create: (reviewData) => authRequest('/review', {
      method: 'POST',
      body: JSON.stringify(reviewData),
    }),
    update: (id, reviewData) => authRequest(`/review/${id}`, {
      method: 'PUT',
      body: JSON.stringify(reviewData),
    }),
    delete: (id) => authRequest(`/review/${id}`, {
      method: 'DELETE',
    }),
  },

  // Category endpoints
  categories: {
    getAll: () => authRequest('/category'),
    getById: (id) => authRequest(`/category/${id}`),
  },

  // User profile endpoints
  profile: {
    get: () => authRequest('/profile'),
    update: (profileData) => authRequest('/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    }),
    getOrderHistory: () => authRequest('/profile/orders'),
    getReviews: () => authRequest('/profile/reviews'),
  },

  // Staff endpoints
  staff: {
    // Dashboard
    getDashboardStats: () => authRequest('/dashboard/stats'),
    getRecentOrders: (count = 5) => authRequest(`/dashboard/recent-orders?count=${count}`),

    // Orders management
    getOrders: (filters = {}) => {
      const queryParams = new URLSearchParams({
        pageNumber: filters.pageNumber || 1,
        pageSize: filters.pageSize || 10,
        ...(filters.status && { status: filters.status }),
        ...(filters.today && { today: true }),
        ...(filters.search && { search: filters.search }),
      });

      return authRequest(`/order/staff?${queryParams}`);
    },
    getOrderById: (id) => authRequest(`/order/${id}`),
    completeOrder: (id) => authRequest(`/order/${id}/complete`, {
      method: 'POST',
    }),
    cancelOrder: (id) => authRequest(`/order/${id}/cancel`, {
      method: 'POST',
    }),
    getOrderByClaimCode: (claimCode) => authRequest(`/order/claim-code/${claimCode}`),
    verifyOrder: (orderId, membershipId) => authRequest(`/order/${orderId}/verify`, {
      method: 'POST',
      body: JSON.stringify({ membershipId }),
    }),

    // Book management (Admin only)
    getBooks: (filters = {}) => {
      const queryParams = new URLSearchParams({
        pageNumber: filters.pageNumber || 1,
        pageSize: filters.pageSize || 10,
        ...(filters.search && { search: filters.search }),
        ...(filters.category && { categoryId: filters.category }),
        ...(filters.lowStock && { lowStock: true }),
      });

      return authRequest(`/book/admin?${queryParams}`);
    },
    createBook: (bookData) => authRequest('/book', {
      method: 'POST',
      body: JSON.stringify(bookData),
    }),
    updateBook: (id, bookData) => authRequest(`/book/${id}`, {
      method: 'PUT',
      body: JSON.stringify(bookData),
    }),
    deleteBook: (id) => authRequest(`/book/${id}`, {
      method: 'DELETE',
    }),
    updateInventory: (id, quantity) => authRequest(`/book/${id}/inventory`, {
      method: 'PUT',
      body: JSON.stringify({ quantity }),
    }),

    // Announcement management (Admin only)
    getAnnouncements: () => authRequest('/announcement'),
    createAnnouncement: (announcementData) => authRequest('/announcement', {
      method: 'POST',
      body: JSON.stringify({
        ...announcementData,
        type: announcementData.type || 'Info' // Default type if not provided
      }),
    }),
    deleteAnnouncement: (id) => authRequest(`/announcement/${id}`, {
      method: 'DELETE',
    }),

    // User management (Admin only)
    getUsers: (filters = {}) => {
      const queryParams = new URLSearchParams({
        pageNumber: filters.pageNumber || 1,
        pageSize: filters.pageSize || 10,
        ...(filters.search && { search: filters.search }),
        ...(filters.role && { role: filters.role }),
      });

      return authRequest(`/user/admin?${queryParams}`);
    },
    getUserById: (id) => authRequest(`/user/${id}`),
    updateUserRole: (id, role) => authRequest(`/user/${id}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    }),
  },
};

export default api;
