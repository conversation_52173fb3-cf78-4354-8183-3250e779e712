import * as signalR from '@microsoft/signalr';

// SignalR hub URL - update this to match your backend hub URL
const HUB_URL = 'http://localhost:5000/booksphere';

class SignalRService {
  constructor() {
    this.connection = null;
    this.connectionPromise = null;
    this.callbacks = {
      onReceiveNotification: [],
      onReceiveAnnouncement: [],
      onConnectionChange: []
    };
  }

  // Initialize the connection
  async start() {
    if (this.connection) {
      return this.connectionPromise;
    }

    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(HUB_URL)
      .withAutomaticReconnect()
      .configureLogging(signalR.LogLevel.Information)
      .build();

    // Set up event handlers
    this.connection.on('ReceiveNotification', (notification) => {
      this.callbacks.onReceiveNotification.forEach(callback => callback(notification));
    });

    this.connection.on('ReceiveAnnouncement', (announcement) => {
      this.callbacks.onReceiveAnnouncement.forEach(callback => callback(announcement));
    });

    // Connection state change handlers
    this.connection.onreconnecting(() => {
      this.notifyConnectionChange('reconnecting');
    });

    this.connection.onreconnected(() => {
      this.notifyConnectionChange('connected');
    });

    this.connection.onclose(() => {
      this.notifyConnectionChange('disconnected');
    });

    // Start the connection
    this.connectionPromise = this.connection.start()
      .then(() => {
        console.log('SignalR connected');
        this.notifyConnectionChange('connected');
        return this.connection;
      })
      .catch(err => {
        console.error('SignalR connection error:', err);
        this.notifyConnectionChange('error');
        throw err;
      });

    return this.connectionPromise;
  }

  // Stop the connection
  async stop() {
    if (this.connection) {
      await this.connection.stop();
      this.connection = null;
      this.connectionPromise = null;
      this.notifyConnectionChange('disconnected');
    }
  }

  // Subscribe to all notifications
  async subscribeToAll() {
    await this.ensureConnection();
    return this.connection.invoke('SubscribeToAll');
  }

  // Unsubscribe from all notifications
  async unsubscribeFromAll() {
    await this.ensureConnection();
    return this.connection.invoke('UnsubscribeFromAll');
  }

  // Subscribe to book notifications
  async subscribeToBook(bookId) {
    await this.ensureConnection();
    return this.connection.invoke('SubscribeToBook', bookId);
  }

  // Unsubscribe from book notifications
  async unsubscribeFromBook(bookId) {
    await this.ensureConnection();
    return this.connection.invoke('UnsubscribeFromBook', bookId);
  }

  // Subscribe to user notifications
  async subscribeToUser(userId) {
    await this.ensureConnection();
    return this.connection.invoke('SubscribeToUser', userId);
  }

  // Unsubscribe from user notifications
  async unsubscribeFromUser(userId) {
    await this.ensureConnection();
    return this.connection.invoke('UnsubscribeFromUser', userId);
  }

  // Register notification callback
  onNotification(callback) {
    this.callbacks.onReceiveNotification.push(callback);
    return () => {
      this.callbacks.onReceiveNotification = this.callbacks.onReceiveNotification.filter(cb => cb !== callback);
    };
  }

  // Register announcement callback
  onAnnouncement(callback) {
    this.callbacks.onReceiveAnnouncement.push(callback);
    return () => {
      this.callbacks.onReceiveAnnouncement = this.callbacks.onReceiveAnnouncement.filter(cb => cb !== callback);
    };
  }

  // Register connection change callback
  onConnectionChange(callback) {
    this.callbacks.onConnectionChange.push(callback);
    return () => {
      this.callbacks.onConnectionChange = this.callbacks.onConnectionChange.filter(cb => cb !== callback);
    };
  }

  // Notify connection change
  notifyConnectionChange(status) {
    this.callbacks.onConnectionChange.forEach(callback => callback(status));
  }

  // Ensure connection is established
  async ensureConnection() {
    if (!this.connection) {
      await this.start();
    } else if (this.connection.state !== signalR.HubConnectionState.Connected) {
      await this.connectionPromise;
    }
  }
}

// Create a singleton instance
const signalRService = new SignalRService();

export default signalRService;
