using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AnnouncementController : BaseController
{
    private readonly IAnnouncementService _announcementService;

    public AnnouncementController(IAnnouncementService announcementService)
    {
        _announcementService = announcementService;
    }

    [HttpGet]
    public async Task<ActionResult<List<AnnouncementDto>>> GetActiveAnnouncements()
    {
        var announcements = await _announcementService.GetActiveAnnouncementsAsync();
        return Ok(announcements);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<AnnouncementDto>> GetAnnouncement(Guid id)
    {
        var announcement = await _announcementService.GetAnnouncementAsync(id);
        return Ok(announcement);
    }

    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<AnnouncementDto>> CreateAnnouncement(CreateAnnouncementDto createAnnouncementDto)
    {
        var adminId = GetUserId();
        var announcement = await _announcementService.CreateAnnouncementAsync(adminId, createAnnouncementDto);
        return CreatedAtAction(nameof(GetAnnouncement), new { id = announcement.Id }, announcement);
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<AnnouncementDto>> UpdateAnnouncement(Guid id, CreateAnnouncementDto updateAnnouncementDto)
    {
        var announcement = await _announcementService.UpdateAnnouncementAsync(id, updateAnnouncementDto);
        return Ok(announcement);
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteAnnouncement(Guid id)
    {
        await _announcementService.DeleteAnnouncementAsync(id);
        return NoContent();
    }

    [HttpPut("{id}/toggle")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<AnnouncementDto>> ToggleAnnouncementStatus(Guid id, [FromBody] bool isActive)
    {
        var announcement = await _announcementService.ToggleAnnouncementStatusAsync(id, isActive);
        return Ok(announcement);
    }

    [HttpGet("type/{type}")]
    public async Task<ActionResult<List<AnnouncementDto>>> GetAnnouncementsByType(string type)
    {
        var announcements = await _announcementService.GetAnnouncementsByTypeAsync(type);
        return Ok(announcements);
    }


}
