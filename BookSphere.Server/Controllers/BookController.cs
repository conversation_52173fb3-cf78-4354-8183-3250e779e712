using System;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
public class BookController : BaseController
{
    private readonly IBookService _bookService;

    public BookController(IBookService bookService)
    {
        _bookService = bookService;
    }

    [HttpGet]
    [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { "pageNumber", "pageSize", "*" })]
    public async Task<ActionResult<PaginatedResponse<BookDto>>> GetBooks(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] BookFIlterDto? filterDto = null)
    {
        var books = await _bookService.GetBookAsync(pageNumber, pageSize, filterDto);
        return Ok(books);
    }

    [HttpGet("{id}")]
    [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { "id" })]
    public async Task<ActionResult<BookDetailsDto>> GetBook(Guid id)
    {
        var book = await _bookService.GetBookDetailsAsync(id);
        return Ok(book);
    }

    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BookDto>> CreateBook(CreateBookDto createBookDto)
    {
        var book = await _bookService.CreateBookAsync(createBookDto);
        return CreatedAtAction(nameof(GetBook), new { id = book.Id }, book);
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BookDto>> UpdateBook(Guid id, UpdateBookDto updateBookDto)
    {
        var book = await _bookService.UpdateBookAsync(id, updateBookDto);
        return Ok(book);
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteBook(Guid id)
    {
        await _bookService.DeleteBookAsync(id);
        return NoContent();
    }

    [HttpPut("{id}/inventory")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BookDto>> UpdateInventory(Guid id, [FromBody] int quantity)
    {
        var book = await _bookService.UpdateInventoryAsync(id, quantity);
        return Ok(book);
    }

    [HttpPut("{id}/discount")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BookDto>> SetDiscount(Guid id, SetDiscountDto discountDto)
    {
        var book = await _bookService.SetDiscountAsync(id, discountDto);
        return Ok(book);
    }
}
