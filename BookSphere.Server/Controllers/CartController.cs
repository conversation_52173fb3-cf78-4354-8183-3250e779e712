using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Member")]
public class CartController : BaseController
{
    private readonly ICartService _cartService;

    public CartController(ICartService cartService)
    {
        _cartService = cartService;
    }

    [HttpGet]
    public async Task<ActionResult<CartDto>> GetCart()
    {
        var userId = GetUserId();
        var cart = await _cartService.GetCartAsync(userId);
        return Ok(cart);
    }

    [HttpPost("add")]
    public async Task<ActionResult<CartDto>> AddToCart(AddToCartDto addToCartDto)
    {
        var userId = GetUserId();
        var cart = await _cartService.AddToCartAsync(userId, addToCartDto);
        return Ok(cart);
    }

    [HttpPut("update")]
    public async Task<ActionResult<CartDto>> UpdateCartItem(UpdateCartItemDto updateCartItemDto)
    {
        var userId = GetUserId();
        var cart = await _cartService.UpdateCartItemAsync(userId, updateCartItemDto);
        return Ok(cart);
    }

    [HttpDelete("remove/{cartItemId}")]
    public async Task<ActionResult<CartDto>> RemoveFromCart(Guid cartItemId)
    {
        var userId = GetUserId();
        var cart = await _cartService.RemoveFromCartAsync(userId, cartItemId);
        return Ok(cart);
    }

    [HttpDelete("clear")]
    public async Task<ActionResult> ClearCart()
    {
        var userId = GetUserId();
        await _cartService.ClearCartAsync(userId);
        return NoContent();
    }

    [HttpGet("validate")]
    public async Task<ActionResult> ValidateCart()
    {
        var userId = GetUserId();
        var isValid = await _cartService.ValidateCartAsync(userId);
        return Ok(new { isValid });
    }


}
