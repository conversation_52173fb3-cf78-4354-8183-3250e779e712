using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OrderController : BaseController
{
    private readonly IOrderService _orderService;

    public OrderController(IOrderService orderService)
    {
        _orderService = orderService;
    }

    [HttpPost]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<OrderDto>> CreateOrder(CreateOrderDto createOrderDto)
    {
        var userId = GetUserId();
        var order = await _orderService.CreateOrderAsync(userId, createOrderDto);
        return CreatedAtAction(nameof(GetOrder), new { id = order.Id }, order);
    }

    [HttpGet("{id}")]
    [Authorize]
    public async Task<ActionResult<OrderDto>> GetOrder(Guid id)
    {
        var order = await _orderService.GetOrderAsync(id);

        // If not admin or staff, check if the order belongs to the user
        if (!User.IsInRole("Admin") && !User.IsInRole("Staff"))
        {
            var userId = GetUserId();
            if (order.Id != id)
            {
                return Forbid();
            }
        }

        return Ok(order);
    }

    [HttpGet("user")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<List<OrderDto>>> GetUserOrders()
    {
        var userId = GetUserId();
        var orders = await _orderService.GetUserOrdersAsync(userId);
        return Ok(orders);
    }

    [HttpPost("{id}/cancel")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<OrderDto>> CancelOrder(Guid id)
    {
        var userId = GetUserId();
        var order = await _orderService.CancelOrderAsync(userId, id);
        return Ok(order);
    }

    [HttpPost("process")]
    [Authorize(Roles = "Admin,Staff")]
    public async Task<ActionResult<OrderDto>> ProcessOrder(ClaimCodeProcessDto claimCodeProcessDto)
    {
        var staffId = User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
        var order = await _orderService.ProcessOrderAsync(staffId, claimCodeProcessDto);
        return Ok(order);
    }

    [HttpGet("pending")]
    [Authorize(Roles = "Admin,Staff")]
    public async Task<ActionResult<List<OrderDto>>> GetPendingOrders()
    {
        var orders = await _orderService.GetPendingOrdersAsync();
        return Ok(orders);
    }

    [HttpPost("{id}/apply-discounts")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<OrderDto>> ApplyDiscounts(Guid id)
    {
        var userId = GetUserId();
        var order = await _orderService.ApplyDiscountsAsync(id, userId);
        return Ok(order);
    }


}
