using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReviewController : BaseController
{
    private readonly IReviewService _reviewService;

    public ReviewController(IReviewService reviewService)
    {
        _reviewService = reviewService;
    }

    [HttpGet("book/{bookId}")]
    public async Task<ActionResult<List<ReviewDto>>> GetBookReviews(Guid bookId)
    {
        var reviews = await _reviewService.GetBookReviewsAsync(bookId);
        return Ok(reviews);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ReviewDto>> GetReview(Guid id)
    {
        var review = await _reviewService.GetReviewAsync(id);
        return Ok(review);
    }

    [HttpPost]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<ReviewDto>> CreateReview(CreateReviewDto createReviewDto)
    {
        var userId = GetUserId();
        var review = await _reviewService.CreateReviewAsync(userId, createReviewDto);
        return CreatedAtAction(nameof(GetReview), new { id = review.Id }, review);
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<ReviewDto>> UpdateReview(Guid id, CreateReviewDto updateReviewDto)
    {
        var userId = GetUserId();
        var review = await _reviewService.UpdateReviewAsync(userId, id, updateReviewDto);
        return Ok(review);
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult> DeleteReview(Guid id)
    {
        var userId = GetUserId();
        await _reviewService.DeleteReviewAsync(userId, id);
        return NoContent();
    }

    [HttpGet("can-review/{bookId}")]
    [Authorize(Roles = "Member")]
    public async Task<ActionResult<bool>> CanReviewBook(Guid bookId)
    {
        var userId = GetUserId();
        var canReview = await _reviewService.CanReviewBookAsync(userId, bookId);
        return Ok(canReview);
    }

    [HttpGet("book/{bookId}/rating")]
    public async Task<ActionResult<double>> GetBookAverageRating(Guid bookId)
    {
        var rating = await _reviewService.GetBookAverageRatingAsync(bookId);
        return Ok(rating);
    }


}
