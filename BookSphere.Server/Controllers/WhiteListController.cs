using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BookSphere.DTOs;
using BookSphere.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BookSphere.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Member")]
public class WhiteListController : BaseController
{
    private readonly IWhiteListService _whiteListService;

    public WhiteListController(IWhiteListService whiteListService)
    {
        _whiteListService = whiteListService;
    }

    [HttpGet]
    public async Task<ActionResult<WhiteListDto>> GetWhiteList()
    {
        var userId = GetUserId();
        var whiteList = await _whiteListService.GetWhiteListAsync(userId);
        return Ok(whiteList);
    }

    [HttpPost("add")]
    public async Task<ActionResult<WhiteListDto>> AddToWhiteList(AddToWhiteListDto addToWhiteListDto)
    {
        var userId = GetUserId();
        var whiteList = await _whiteListService.AddToWhiteListAsync(userId, addToWhiteListDto);
        return Ok(whiteList);
    }

    [HttpDelete("remove/{bookId}")]
    public async Task<ActionResult<WhiteListDto>> RemoveFromWhiteList(Guid bookId)
    {
        var userId = GetUserId();
        var whiteList = await _whiteListService.RemoveFromWhiteListAsync(userId, bookId);
        return Ok(whiteList);
    }

    [HttpGet("check/{bookId}")]
    public async Task<ActionResult<bool>> IsInWhiteList(Guid bookId)
    {
        var userId = GetUserId();
        var isInWhiteList = await _whiteListService.IsInWhiteListAsync(userId, bookId);
        return Ok(isInWhiteList);
    }

    [HttpPost("move-to-cart/{bookId}")]
    public async Task<ActionResult<CartDto>> MoveToCart(Guid bookId, [FromBody] int quantity)
    {
        var userId = GetUserId();
        var cart = await _whiteListService.MoveToCartAsync(userId, bookId, quantity);
        return Ok(cart);
    }


}
