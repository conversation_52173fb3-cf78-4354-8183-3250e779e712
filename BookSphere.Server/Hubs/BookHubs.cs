using System;
using System.Threading.Tasks;
using BookSphere.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace BookSphere.Hubs;

public class BookHubs : Hub
{
    private readonly ILogger<BookHubs> _logger;

    public BookHubs(ILogger<BookHubs> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        try
        {
            _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);

            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = "Connected to BookSphere real-time notifications",
                timestamp = DateTime.UtcNow
            });

            await base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in OnConnectedAsync");
            throw;
        }
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        try
        {
            _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);

            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = "Disconnected from BookSphere real-time notifications",
                timestamp = DateTime.UtcNow
            });

            await base.OnDisconnectedAsync(exception);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in OnDisconnectedAsync");
            throw;
        }
    }

    // Method for clients to join specific notification groups
    public async Task JoinGroup(string groupName)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} joining group: {GroupName}", Context.ConnectionId, groupName);

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Joined group: {groupName}",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in JoinGroup for group {GroupName}", groupName);
            throw;
        }
    }

    // Method for clients to leave specific notification groups
    public async Task LeaveGroup(string groupName)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} leaving group: {GroupName}", Context.ConnectionId, groupName);

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Left group: {groupName}",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in LeaveGroup for group {GroupName}", groupName);
            throw;
        }
    }

    // Method to subscribe to specific user's notifications
    public async Task SubscribeToUser(Guid userId)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} subscribing to user: {UserId}", Context.ConnectionId, userId);

            await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Subscribed to user {userId} notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SubscribeToUser for user {UserId}", userId);
            throw;
        }
    }

    // Method to unsubscribe from specific user's notifications
    public async Task UnsubscribeFromUser(Guid userId)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} unsubscribing from user: {UserId}", Context.ConnectionId, userId);

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"user_{userId}");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Unsubscribed from user {userId} notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UnsubscribeFromUser for user {UserId}", userId);
            throw;
        }
    }

    // Method to subscribe to book notifications
    public async Task SubscribeToBook(Guid bookId)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} subscribing to book: {BookId}", Context.ConnectionId, bookId);

            await Groups.AddToGroupAsync(Context.ConnectionId, $"book_{bookId}");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Subscribed to book {bookId} notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SubscribeToBook for book {BookId}", bookId);
            throw;
        }
    }

    // Method to unsubscribe from book notifications
    public async Task UnsubscribeFromBook(Guid bookId)
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} unsubscribing from book: {BookId}", Context.ConnectionId, bookId);

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"book_{bookId}");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = $"Unsubscribed from book {bookId} notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UnsubscribeFromBook for book {BookId}", bookId);
            throw;
        }
    }

    // Method to subscribe to all notifications
    public async Task SubscribeToAll()
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} subscribing to all notifications", Context.ConnectionId);

            await Groups.AddToGroupAsync(Context.ConnectionId, "all");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = "Subscribed to all notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SubscribeToAll");
            throw;
        }
    }

    // Method to unsubscribe from all notifications
    public async Task UnsubscribeFromAll()
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} unsubscribing from all notifications", Context.ConnectionId);

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "all");
            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = "Unsubscribed from all notifications",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UnsubscribeFromAll");
            throw;
        }
    }

    // Method to get current connection status
    public async Task GetConnectionStatus()
    {
        try
        {
            _logger.LogInformation("Client {ConnectionId} checking connection status", Context.ConnectionId);

            await Clients.Caller.SendAsync("ReceiveNotification", new
            {
                type = "system",
                message = "Connection status check",
                connectionId = Context.ConnectionId,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetConnectionStatus");
            throw;
        }
    }

    // Method to broadcast successful order
    [Authorize(Roles = "Admin,Staff")]
    public async Task BroadcastSuccessfulOrder(Guid orderId, Guid userId, string bookTitle, string memberName)
    {
        try
        {
            _logger.LogInformation("Broadcasting successful order: {OrderId}", orderId);

            await Clients.Group("all").SendAsync("ReceiveNotification", new
            {
                type = "order",
                message = $"{memberName} just purchased '{bookTitle}'!",
                orderId = orderId,
                userId = userId,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in BroadcastSuccessfulOrder for order {OrderId}", orderId);
            throw;
        }
    }

    // Method to send announcement
    [Authorize(Roles = "Admin")]
    public async Task SendAnnouncement(Guid announcementId, string title, string content, string type)
    {
        try
        {
            _logger.LogInformation("Sending announcement: {AnnouncementId}", announcementId);

            await Clients.Group("all").SendAsync("ReceiveAnnouncement", new
            {
                type = type, // "New Arrival", "Info", "Deal"
                title = title,
                content = content,
                announcementId = announcementId,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SendAnnouncement for announcement {AnnouncementId}", announcementId);
            throw;
        }
    }

    // Method to notify about new book arrival
    [Authorize(Roles = "Admin")]
    public async Task NotifyNewBookArrival(Guid bookId, string title, string author, string genre)
    {
        try
        {
            _logger.LogInformation("Notifying about new book arrival: {BookId}", bookId);

            await Clients.Group("all").SendAsync("ReceiveNotification", new
            {
                type = "new_arrival",
                message = $"New book arrival: '{title}' by {author}",
                bookId = bookId,
                title = title,
                author = author,
                genre = genre,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in NotifyNewBookArrival for book {BookId}", bookId);
            throw;
        }
    }

    // Method to notify about book discount
    [Authorize(Roles = "Admin")]
    public async Task NotifyBookDiscount(Guid bookId, string title, int discountPercentage, DateTime endDate)
    {
        try
        {
            _logger.LogInformation("Notifying about book discount: {BookId}, {DiscountPercentage}%", bookId, discountPercentage);

            await Clients.Group("all").SendAsync("ReceiveNotification", new
            {
                type = "discount",
                message = $"Book on sale: '{title}' with {discountPercentage}% discount!",
                bookId = bookId,
                title = title,
                discountPercentage = discountPercentage,
                endDate = endDate,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in NotifyBookDiscount for book {BookId}", bookId);
            throw;
        }
    }
}
