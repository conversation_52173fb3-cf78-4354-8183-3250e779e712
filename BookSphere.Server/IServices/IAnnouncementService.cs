using System;
using BookSphere.DTOs;

namespace BookSphere.IServices;

public interface IAnnouncementService
{
    // Get all active announcements
    Task<List<AnnouncementDto>> GetActiveAnnouncementsAsync();
    
    // Get announcement by ID
    Task<AnnouncementDto> GetAnnouncementAsync(Guid announcementId);
    
    // Create a new announcement
    Task<AnnouncementDto> CreateAnnouncementAsync(Guid adminId, CreateAnnouncementDto createAnnouncementDto);
    
    // Update an announcement
    Task<AnnouncementDto> UpdateAnnouncementAsync(Guid announcementId, CreateAnnouncementDto updateAnnouncementDto);
    
    // Delete an announcement
    Task<bool> DeleteAnnouncementAsync(Guid announcementId);
    
    // Activate/Deactivate an announcement
    Task<AnnouncementDto> ToggleAnnouncementStatusAsync(Guid announcementId, bool isActive);
    
    // Get announcements by type
    Task<List<AnnouncementDto>> GetAnnouncementsByTypeAsync(string type);
}
