using System;
using BookSphere.DTOs;

namespace BookSphere.IServices;

public interface IOrderService
{
    // Create a new order from cart
    Task<OrderDto> CreateOrderAsync(Guid userId, CreateOrderDto createOrderDto);
    
    // Get order by ID
    Task<OrderDto> GetOrderAsync(Guid orderId);
    
    // Get all orders for a user
    Task<List<OrderDto>> GetUserOrdersAsync(Guid userId);
    
    // Cancel an order
    Task<OrderDto> CancelOrderAsync(Guid userId, Guid orderId);
    
    // Process an order with claim code (for staff)
    Task<OrderDto> ProcessOrderAsync(string staffId, ClaimCodeProcessDto claimCodeProcessDto);
    
    // Get all pending orders (for staff/admin)
    Task<List<OrderDto>> GetPendingOrdersAsync();
    
    // Apply discounts to order
    Task<OrderDto> ApplyDiscountsAsync(Guid orderId, Guid userId);
    
    // Generate claim code
    Task<string> GenerateClaimCodeAsync();
    
    // Send order confirmation email
    Task SendOrderConfirmationAsync(Guid orderId);
}
