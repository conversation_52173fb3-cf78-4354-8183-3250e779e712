using System.Text;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.Hubs;
using BookSphere.IServices;
using BookSphere.Middleware;
using BookSphere.Services;
using BookSphere.Validators;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    });

// Add FluentValidation
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssemblyContaining<RegisterDtoValidator>();

builder.Services.AddDbContext<BookSphereDbContext>(options =>
        options.UseNpgsql(
                builder.Configuration.GetConnectionString("DB"),
                NpgSqlOptions => NpgSqlOptions.MigrationsAssembly("BookSphere")
        )
);

builder.Services.AddAuthentication(options =>
{
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
        options.TokenValidationParameters = new TokenValidationParameters
        {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = builder.Configuration["Jwt:Issuer"],
                ValidAudience = builder.Configuration["Jwt:Audience"],
                IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]!)
                )
        };

        options.Events = new JwtBearerEvents
        {
                OnMessageReceived = context =>
                {
                        var accessToken = context.Request.Query["access_token"];
                        var path = context.HttpContext.Request.Path;

                        if (!string.IsNullOrEmpty(accessToken) &&
                            (path.StartsWithSegments("/booksphere") || path.StartsWithSegments("/hubs/notifications")))
                        {
                                context.Token = accessToken;
                        }

                        return Task.CompletedTask;
                }
        };
});

builder.Services.AddAuthorization(options =>
{
        options.AddPolicy("RequireAdminRole", policy => policy.RequireRole("Admin"));
        options.AddPolicy("RequireStaffRole", policy => policy.RequireRole("Staff, Admin"));
        options.AddPolicy("RequireMemberRole", policy => policy.RequireRole("Member"));
});

builder.Services.AddCors(options =>
{
        options.AddPolicy("AllowReactApp", policy =>
        {
                policy.WithOrigins(builder.Configuration["AllowedOrigins"]!.Split(';'))
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials(); //Need this for signalR
                });
});

builder.Services.AddSignalR(options =>
{
        options.EnableDetailedErrors = true;
});

builder.Services.AddAutoMapper(typeof(Program)); // Using auto mapper

// Register services
builder.Services.AddScoped<IBookService, BookService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<ICartService, CartService>();
builder.Services.AddScoped<IWhiteListService, WhiteListService>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IReviewService, ReviewService>();
builder.Services.AddScoped<IAnnouncementService, AnnouncementService>();

// Register HttpContextAccessor for accessing the current user
builder.Services.AddHttpContextAccessor();

// Add response caching
builder.Services.AddResponseCaching();

// Add memory cache
builder.Services.AddMemoryCache();

// Add Rate Limiting
builder.Services.AddRateLimiter(options =>
{
    options.GlobalLimiter = Microsoft.AspNetCore.RateLimiting.PartitionedRateLimiter.Create<Microsoft.AspNetCore.Http.HttpContext, string>(context =>
    {
        return Microsoft.AspNetCore.RateLimiting.RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: context.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
            factory: partition => new Microsoft.AspNetCore.RateLimiting.FixedWindowRateLimiterOptions
            {
                AutoReplenishment = true,
                PermitLimit = 100,
                QueueLimit = 0,
                Window = TimeSpan.FromMinutes(1)
            });
    });

    options.OnRejected = async (context, token) =>
    {
        context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
        context.HttpContext.Response.ContentType = "application/json";

        await context.HttpContext.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new
        {
            error = "Too many requests. Please try again later.",
            retryAfter = 60
        }), token);
    };
});

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<BookSphereDbContext>("database")
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());


var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "BookSphere API v1"));
}

app.UseHttpsRedirection();

app.UseMiddleware<ErrorHandlingMiddleware>(); //for handling errors and exceptions

// Add response caching middleware
app.UseResponseCaching();

// Add rate limiting middleware
app.UseRateLimiter();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.UseCors("AllowReactApp");

app.MapHub<BookHubs>("/booksphere"); //sends to everyone of domain that has /booksphere
app.MapHub<BookHubs>("/hubs/notifications"); //additional endpoint for notifications

// Map health checks
app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var result = System.Text.Json.JsonSerializer.Serialize(new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(e => new
            {
                name = e.Key,
                status = e.Value.Status.ToString(),
                description = e.Value.Description
            })
        });
        await context.Response.WriteAsync(result);
    }
});

app.Run();
