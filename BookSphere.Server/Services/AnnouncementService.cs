using System;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.Hubs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class AnnouncementService : IAnnouncementService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IUserService _userService;
    private readonly IHubContext<BookHubs> _hubContext;

    public AnnouncementService(
        BookSphereDbContext context,
        IMapper mapper,
        IUserService userService,
        IHubContext<BookHubs> hubContext)
    {
        _context = context;
        _mapper = mapper;
        _userService = userService;
        _hubContext = hubContext;
    }

    public async Task<List<AnnouncementDto>> GetActiveAnnouncementsAsync()
    {
        var now = DateTime.UtcNow;
        
        var announcements = await _context.Announcements
            .Where(a => a.IsActive && a.StartDate <= now && a.EndDate >= now)
            .OrderByDescending(a => a.CreatedDate)
            .ToListAsync();

        return _mapper.Map<List<AnnouncementDto>>(announcements);
    }

    public async Task<AnnouncementDto> GetAnnouncementAsync(Guid announcementId)
    {
        var announcement = await _context.Announcements
            .FirstOrDefaultAsync(a => a.Id == announcementId);

        if (announcement == null)
        {
            throw new KeyNotFoundException("Announcement not found");
        }

        return _mapper.Map<AnnouncementDto>(announcement);
    }

    public async Task<AnnouncementDto> CreateAnnouncementAsync(Guid adminId, CreateAnnouncementDto createAnnouncementDto)
    {
        // Check if admin exists
        var admin = await _context.Users.FindAsync(adminId);
        if (admin == null)
        {
            throw new KeyNotFoundException("Admin not found");
        }

        // Check if admin has admin role
        if (admin.Role != "Admin")
        {
            throw new UnauthorizedAccessException("Only admins can create announcements");
        }

        // Create announcement
        var announcement = _mapper.Map<Announcement>(createAnnouncementDto);
        announcement.CreatedById = adminId;
        announcement.CreatedDate = DateTime.UtcNow;
        announcement.IsActive = true;

        // Add to database
        await _context.Announcements.AddAsync(announcement);
        await _context.SaveChangesAsync();

        // Broadcast announcement
        await BroadcastAnnouncementAsync(announcement);

        return _mapper.Map<AnnouncementDto>(announcement);
    }

    public async Task<AnnouncementDto> UpdateAnnouncementAsync(Guid announcementId, CreateAnnouncementDto updateAnnouncementDto)
    {
        var announcement = await _context.Announcements.FindAsync(announcementId);
        if (announcement == null)
        {
            throw new KeyNotFoundException("Announcement not found");
        }

        // Update properties
        announcement.Title = updateAnnouncementDto.Title;
        announcement.Content = updateAnnouncementDto.Content;
        announcement.StartDate = updateAnnouncementDto.StartDate;
        announcement.EndDate = updateAnnouncementDto.EndDate;
        announcement.Type = updateAnnouncementDto.Type;
        announcement.ImageUrl = updateAnnouncementDto.ImageUrl;

        // Save changes
        await _context.SaveChangesAsync();

        // Broadcast updated announcement if active
        if (announcement.IsActive)
        {
            await BroadcastAnnouncementAsync(announcement);
        }

        return _mapper.Map<AnnouncementDto>(announcement);
    }

    public async Task<bool> DeleteAnnouncementAsync(Guid announcementId)
    {
        var announcement = await _context.Announcements.FindAsync(announcementId);
        if (announcement == null)
        {
            throw new KeyNotFoundException("Announcement not found");
        }

        // Remove from database
        _context.Announcements.Remove(announcement);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<AnnouncementDto> ToggleAnnouncementStatusAsync(Guid announcementId, bool isActive)
    {
        var announcement = await _context.Announcements.FindAsync(announcementId);
        if (announcement == null)
        {
            throw new KeyNotFoundException("Announcement not found");
        }

        // Update status
        announcement.IsActive = isActive;
        await _context.SaveChangesAsync();

        // Broadcast if activated
        if (isActive)
        {
            await BroadcastAnnouncementAsync(announcement);
        }

        return _mapper.Map<AnnouncementDto>(announcement);
    }

    public async Task<List<AnnouncementDto>> GetAnnouncementsByTypeAsync(string type)
    {
        var now = DateTime.UtcNow;
        
        var announcements = await _context.Announcements
            .Where(a => a.IsActive && a.Type == type && a.StartDate <= now && a.EndDate >= now)
            .OrderByDescending(a => a.CreatedDate)
            .ToListAsync();

        return _mapper.Map<List<AnnouncementDto>>(announcements);
    }

    // Helper method to broadcast announcement
    private async Task BroadcastAnnouncementAsync(Announcement announcement)
    {
        await _hubContext.Clients.Group("all").SendAsync("ReceiveAnnouncement", new
        {
            type = announcement.Type,
            title = announcement.Title,
            content = announcement.Content,
            announcementId = announcement.Id,
            timestamp = DateTime.UtcNow
        });
    }
}
