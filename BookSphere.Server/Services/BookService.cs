using System;
using System.Linq;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class BookService : IBookService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;

    public BookService(BookSphereDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PaginatedResponse<BookDto>> GetBookAsync(int pageNumber, int pageSize, BookFIlterDto? filterDto)
    {
        // Start with all books
        IQueryable<Book> query = _context.Books
            .Include(b => b.Reviews);

        // Apply filters if provided
        if (filterDto != null)
        {
            // Search term (title, author, ISBN, description)
            if (!string.IsNullOrEmpty(filterDto.SearchTerm))
            {
                var searchTerm = filterDto.SearchTerm.ToLower();
                query = query.Where(b => 
                    b.Title.ToLower().Contains(searchTerm) ||
                    b.Author.ToLower().Contains(searchTerm) ||
                    b.ISBN.ToLower().Contains(searchTerm) ||
                    b.Description.ToLower().Contains(searchTerm));
            }

            // Author
            if (!string.IsNullOrEmpty(filterDto.Author))
            {
                query = query.Where(b => b.Author.ToLower().Contains(filterDto.Author.ToLower()));
            }

            // Genre
            if (!string.IsNullOrEmpty(filterDto.Genre))
            {
                query = query.Where(b => b.Genre.ToLower().Contains(filterDto.Genre.ToLower()));
            }

            // In stock
            if (filterDto.InStock.HasValue)
            {
                query = filterDto.InStock.Value 
                    ? query.Where(b => b.StockQuantity > 0)
                    : query.Where(b => b.StockQuantity == 0);
            }

            // Physical library access
            if (filterDto.PhysicalLibraryAccess.HasValue)
            {
                query = query.Where(b => b.PhysicalLibraryAccess == filterDto.PhysicalLibraryAccess.Value);
            }

            // Price range
            if (filterDto.MinPrice.HasValue)
            {
                query = query.Where(b => b.Price >= filterDto.MinPrice.Value);
            }

            if (filterDto.MaxPrice.HasValue)
            {
                query = query.Where(b => b.Price <= filterDto.MaxPrice.Value);
            }

            // Rating
            if (filterDto.MinRating.HasValue)
            {
                query = query.Where(b => b.Reviews.Count > 0 && b.Reviews.Average(r => r.Rating) >= filterDto.MinRating.Value);
            }

            // Language
            if (!string.IsNullOrEmpty(filterDto.Language))
            {
                query = query.Where(b => b.Language.ToLower() == filterDto.Language.ToLower());
            }

            // Format
            if (!string.IsNullOrEmpty(filterDto.Format))
            {
                query = query.Where(b => b.Format.ToLower() == filterDto.Format.ToLower());
            }

            // Publisher
            if (!string.IsNullOrEmpty(filterDto.Publisher))
            {
                query = query.Where(b => b.Publisher.ToLower().Contains(filterDto.Publisher.ToLower()));
            }

            // On sale
            if (filterDto.IsOnSale.HasValue)
            {
                query = query.Where(b => b.IsOnSale == filterDto.IsOnSale.Value);
            }

            // Award winner
            if (filterDto.IsAwardWinner.HasValue)
            {
                query = query.Where(b => b.IsAwardWinner == filterDto.IsAwardWinner.Value);
            }

            // Bestseller
            if (filterDto.IsBestseller.HasValue)
            {
                query = query.Where(b => b.IsBestSeller == filterDto.IsBestseller.Value);
            }

            // New release (published in the last 3 months)
            if (filterDto.IsNewRelease.HasValue && filterDto.IsNewRelease.Value)
            {
                var threeMonthsAgo = DateTime.UtcNow.AddMonths(-3);
                query = query.Where(b => b.PublicationDate >= threeMonthsAgo);
            }

            // New arrival (listed in the last month)
            if (filterDto.IsNewArrival.HasValue && filterDto.IsNewArrival.Value)
            {
                var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
                query = query.Where(b => b.ListedDate >= oneMonthAgo);
            }

            // Coming soon
            if (filterDto.IsComingSoon.HasValue)
            {
                query = query.Where(b => b.IsComingSoon == filterDto.IsComingSoon.Value);
            }

            // Apply sorting
            if (!string.IsNullOrEmpty(filterDto.SortBy))
            {
                switch (filterDto.SortBy.ToLower())
                {
                    case "title":
                        query = filterDto.SortDescending 
                            ? query.OrderByDescending(b => b.Title)
                            : query.OrderBy(b => b.Title);
                        break;
                    case "publicationdate":
                        query = filterDto.SortDescending 
                            ? query.OrderByDescending(b => b.PublicationDate)
                            : query.OrderBy(b => b.PublicationDate);
                        break;
                    case "price":
                        query = filterDto.SortDescending 
                            ? query.OrderByDescending(b => b.Price)
                            : query.OrderBy(b => b.Price);
                        break;
                    case "popularity":
                        query = filterDto.SortDescending 
                            ? query.OrderByDescending(b => b.SoldCount)
                            : query.OrderBy(b => b.SoldCount);
                        break;
                    default:
                        query = query.OrderBy(b => b.Title);
                        break;
                }
            }
            else
            {
                // Default sorting by title
                query = query.OrderBy(b => b.Title);
            }
        }
        else
        {
            // Default sorting by title
            query = query.OrderBy(b => b.Title);
        }

        // Get total count
        var totalCount = await query.CountAsync();

        // Apply pagination
        var books = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        // Map to DTOs
        var bookDtos = _mapper.Map<List<BookDto>>(books);

        // Return paginated response
        return new PaginatedResponse<BookDto>
        {
            Items = bookDtos,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    public async Task<BookDetailsDto> GetBookDetailsAsync(Guid bookId)
    {
        var book = await _context.Books
            .Include(b => b.Reviews)
            .ThenInclude(r => r.User)
            .FirstOrDefaultAsync(b => b.Id == bookId);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        return _mapper.Map<BookDetailsDto>(book);
    }

    public async Task<BookDto> CreateBookAsync(CreateBookDto createDto)
    {
        // Check if ISBN already exists
        if (await _context.Books.AnyAsync(b => b.ISBN == createDto.ISBN))
        {
            throw new ArgumentException("ISBN already exists");
        }

        // Map DTO to entity
        var book = _mapper.Map<Book>(createDto);
        
        // Set default values
        book.ListedDate = DateTime.UtcNow;
        book.SoldCount = 0;

        // Add to database
        await _context.Books.AddAsync(book);
        await _context.SaveChangesAsync();

        return _mapper.Map<BookDto>(book);
    }

    public async Task<BookDto> UpdateBookAsync(Guid id, UpdateBookDto updateDto)
    {
        var book = await _context.Books.FindAsync(id);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Check if ISBN is being changed and if it already exists
        if (updateDto.ISBN != book.ISBN && await _context.Books.AnyAsync(b => b.ISBN == updateDto.ISBN))
        {
            throw new ArgumentException("ISBN already exists");
        }

        // Update properties
        _mapper.Map(updateDto, book);

        // Save changes
        await _context.SaveChangesAsync();

        return _mapper.Map<BookDto>(book);
    }

    public async Task<bool> DeleteBookAsync(Guid id)
    {
        var book = await _context.Books.FindAsync(id);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Check if book is referenced in any orders
        var hasOrders = await _context.OrderItems.AnyAsync(oi => oi.BookId == id);
        if (hasOrders)
        {
            throw new InvalidOperationException("Cannot delete book that has been ordered");
        }

        // Remove from database
        _context.Books.Remove(book);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<BookDto> UpdateInventoryAsync(Guid bookId, int quantity)
    {
        var book = await _context.Books.FindAsync(bookId);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        if (quantity < 0)
        {
            throw new ArgumentException("Quantity cannot be negative");
        }

        book.StockQuantity = quantity;
        await _context.SaveChangesAsync();

        return _mapper.Map<BookDto>(book);
    }

    public async Task<BookDto> SetDiscountAsync(Guid bookId, SetDiscountDto discountDto)
    {
        var book = await _context.Books.FindAsync(bookId);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        if (discountDto.DiscountPercentage < 0 || discountDto.DiscountPercentage > 100)
        {
            throw new ArgumentException("Discount percentage must be between 0 and 100");
        }

        book.DiscountPercentage = discountDto.DiscountPercentage;
        book.IsOnSale = discountDto.IsOnSale;
        book.DiscountStartDate = discountDto.StartDate;
        book.DiscountEndDate = discountDto.EndDate;

        await _context.SaveChangesAsync();

        return _mapper.Map<BookDto>(book);
    }

    public async Task<bool> IsInStockAsync(Guid bookId, int quantity)
    {
        var book = await _context.Books.FindAsync(bookId);

        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        return book.StockQuantity >= quantity;
    }
}
