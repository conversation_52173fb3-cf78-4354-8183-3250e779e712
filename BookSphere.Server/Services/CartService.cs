using System;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class CartService : ICartService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IUserService _userService;
    private readonly IBookService _bookService;

    public CartService(
        BookSphereDbContext context, 
        IMapper mapper, 
        IUserService userService,
        IBookService bookService)
    {
        _context = context;
        _mapper = mapper;
        _userService = userService;
        _bookService = bookService;
    }

    public async Task<CartDto> GetCartAsync(Guid userId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get cart with items and books
        var cart = await _context.Carts
            .Include(c => c.Items)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null)
        {
            throw new KeyNotFoundException("Cart not found");
        }

        // Map to DTO
        var cartDto = _mapper.Map<CartDto>(cart);

        // Calculate totals
        await CalculateCartTotals(cartDto, userId);

        return cartDto;
    }

    public async Task<CartDto> AddToCartAsync(Guid userId, AddToCartDto addToCartDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Check if book exists and is in stock
        if (!await _bookService.IsInStockAsync(addToCartDto.BookId, addToCartDto.Quantity))
        {
            throw new InvalidOperationException("Book is not in stock with the requested quantity");
        }

        // Get cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null)
        {
            // Create cart if it doesn't exist
            cart = new Cart
            {
                UserId = userId,
                LastUpdated = DateTime.UtcNow,
                Items = new List<CartItem>()
            };
            await _context.Carts.AddAsync(cart);
        }
        // Check if item already exists in cart
        var existingItem = cart.Items.FirstOrDefault(i => i.BookId == addToCartDto.BookId);

        if (existingItem != null)
        {
            // Update quantity
            existingItem.Quantity += addToCartDto.Quantity;
            existingItem.AddedDate = DateTime.UtcNow;
        }
        else
        {
            // Add new item
            var newItem = new CartItem
            {
                CartId = cart.Id,
                BookId = addToCartDto.BookId,
                Quantity = addToCartDto.Quantity,
                AddedDate = DateTime.UtcNow
            };
            cart.Items.Add(newItem);
        }

        // Update cart
        cart.LastUpdated = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        // Get updated cart with books
        return await GetCartAsync(userId);
    }

    public async Task<CartDto> UpdateCartItemAsync(Guid userId, UpdateCartItemDto updateCartItemDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null)
        {
            throw new KeyNotFoundException("Cart not found");
        }

        // Find cart item
        var cartItem = cart.Items.FirstOrDefault(i => i.Id == updateCartItemDto.CartItemId);

        if (cartItem == null)
        {
            throw new KeyNotFoundException("Cart item not found");
        }

        // Check if book is in stock with the new quantity
        if (!await _bookService.IsInStockAsync(cartItem.BookId, updateCartItemDto.Quantity))
        {
            throw new InvalidOperationException("Book is not in stock with the requested quantity");
        }

        // Update quantity
        cartItem.Quantity = updateCartItemDto.Quantity;
        cartItem.AddedDate = DateTime.UtcNow;

        // Update cart
        cart.LastUpdated = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        // Get updated cart with books
        return await GetCartAsync(userId);
    }

    public async Task<CartDto> RemoveFromCartAsync(Guid userId, Guid cartItemId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null)
        {
            throw new KeyNotFoundException("Cart not found");
        }

        // Find cart item
        var cartItem = cart.Items.FirstOrDefault(i => i.Id == cartItemId);

        if (cartItem == null)
        {
            throw new KeyNotFoundException("Cart item not found");
        }

        // Remove item
        cart.Items.Remove(cartItem);
        _context.CartItems.Remove(cartItem);

        // Update cart
        cart.LastUpdated = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        // Get updated cart with books
        return await GetCartAsync(userId);
    }

    public async Task<bool> ClearCartAsync(Guid userId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null)
        {
            throw new KeyNotFoundException("Cart not found");
        }

        // Remove all items
        _context.CartItems.RemoveRange(cart.Items);
        cart.Items.Clear();

        // Update cart
        cart.LastUpdated = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ValidateCartAsync(Guid userId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null || !cart.Items.Any())
        {
            throw new InvalidOperationException("Cart is empty");
        }

        // Check if all items are in stock
        foreach (var item in cart.Items)
        {
            if (item.Book.StockQuantity < item.Quantity)
            {
                throw new InvalidOperationException($"Book '{item.Book.Title}' is not in stock with the requested quantity");
            }
        }

        return true;
    }

    // Helper method to calculate cart totals
    private async Task CalculateCartTotals(CartDto cartDto, Guid userId)
    {
        decimal subtotal = 0;
        decimal discountAmount = 0;

        // Calculate subtotal and item discounts
        foreach (var item in cartDto.Items)
        {
            var originalPrice = item.UnitPrice * item.Quantity;
            var discountedPrice = item.DiscountedPrice * item.Quantity;
            
            subtotal += originalPrice;
            discountAmount += (originalPrice - discountedPrice);
        }

        cartDto.SubTotal = subtotal;
        cartDto.DiscountAmount = discountAmount;

        // Check if cart qualifies for bulk discount (5+ books)
        int totalQuantity = cartDto.Items.Sum(i => i.Quantity);
        cartDto.QualifiesForBulkDiscount = totalQuantity >= 5;

        // Check if user has loyalty discount
        cartDto.HasLoyaltyDiscount = await _userService.HasStackableDiscount(userId);

        // Calculate total
        cartDto.Total = subtotal - discountAmount;

        // Apply bulk discount if applicable (5%)
        if (cartDto.QualifiesForBulkDiscount)
        {
            decimal bulkDiscount = cartDto.Total * 0.05m;
            cartDto.DiscountAmount += bulkDiscount;
            cartDto.Total -= bulkDiscount;
        }

        // Apply loyalty discount if applicable (10%)
        if (cartDto.HasLoyaltyDiscount)
        {
            decimal loyaltyDiscount = cartDto.Total * 0.1m;
            cartDto.DiscountAmount += loyaltyDiscount;
            cartDto.Total -= loyaltyDiscount;
        }
    }
}
