using System;
using System.Text;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.Hubs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class OrderService : IOrderService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IUserService _userService;
    private readonly IBookService _bookService;
    private readonly ICartService _cartService;
    private readonly IHubContext<BookHubs> _hubContext;
    private readonly Random _random = new Random();

    public OrderService(
        BookSphereDbContext context,
        IMapper mapper,
        IUserService userService,
        IBookService bookService,
        ICartService cartService,
        IHubContext<BookHubs> hubContext)
    {
        _context = context;
        _mapper = mapper;
        _userService = userService;
        _bookService = bookService;
        _cartService = cartService;
        _hubContext = hubContext;
    }

    public async Task<OrderDto> CreateOrderAsync(Guid userId, CreateOrderDto createOrderDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Validate cart
        await _cartService.ValidateCartAsync(userId);

        // Get user's cart
        var cart = await _context.Carts
            .Include(c => c.Items)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(c => c.UserId == userId);

        if (cart == null || !cart.Items.Any())
        {
            throw new InvalidOperationException("Cart is empty");
        }

        // Create new order
        var order = new Order
        {
            UserId = userId,
            ClaimCode = await GenerateClaimCodeAsync(),
            OrderDate = DateTime.UtcNow,
            Status = "Pending",
            OrderItems = new List<OrderItem>()
        };

        // Calculate totals
        decimal totalAmount = 0;
        decimal discountAmount = 0;

        // Add items to order
        foreach (var cartItem in cart.Items)
        {
            var book = cartItem.Book;
            
            // Calculate prices
            decimal unitPrice = book.Price;
            decimal discountPercentage = book.DiscountPercentage;
            decimal discountedPrice = unitPrice - (unitPrice * discountPercentage / 100);
            decimal subtotal = discountedPrice * cartItem.Quantity;
            
            // Add to totals
            totalAmount += unitPrice * cartItem.Quantity;
            discountAmount += (unitPrice - discountedPrice) * cartItem.Quantity;

            // Create order item
            var orderItem = new OrderItem
            {
                OrderId = order.Id,
                BookId = book.Id,
                Quantity = cartItem.Quantity,
                UnitPrice = unitPrice,
                DiscountPercentage = discountPercentage,
                SubTotal = subtotal
            };
            
            order.OrderItems.Add(orderItem);

            // Update book stock
            book.StockQuantity -= cartItem.Quantity;
        }

        // Set order amounts
        order.TotalAmount = totalAmount;
        order.DecimalAmount = discountAmount;
        order.FinalAmount = totalAmount - discountAmount;

        // Check for bulk discount (5+ books)
        int totalQuantity = cart.Items.Sum(i => i.Quantity);
        if (totalQuantity >= 5)
        {
            decimal bulkDiscount = order.FinalAmount * 0.05m;
            order.AppliedBulkDiscount = true;
            order.DecimalAmount += bulkDiscount;
            order.FinalAmount -= bulkDiscount;
        }

        // Check for loyalty discount (after 10 successful orders)
        var user = await _context.Users.FindAsync(userId);
        if (user != null && user.HasStackableDiscount)
        {
            decimal loyaltyDiscount = order.FinalAmount * 0.1m;
            order.AppliedLoyaltyDiscount = true;
            order.DecimalAmount += loyaltyDiscount;
            order.FinalAmount -= loyaltyDiscount;
            
            // Use the discount
            user.HasStackableDiscount = false;
        }

        // Add order to database
        await _context.Orders.AddAsync(order);
        
        // Clear cart
        await _cartService.ClearCartAsync(userId);
        
        // Save all changes
        await _context.SaveChangesAsync();

        // Send order confirmation email
        await SendOrderConfirmationAsync(order.Id);

        // Return order DTO
        return _mapper.Map<OrderDto>(order);
    }

    public async Task<OrderDto> GetOrderAsync(Guid orderId)
    {
        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(o => o.Id == orderId);

        if (order == null)
        {
            throw new KeyNotFoundException("Order not found");
        }

        return _mapper.Map<OrderDto>(order);
    }

    public async Task<List<OrderDto>> GetUserOrdersAsync(Guid userId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        var orders = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .Where(o => o.UserId == userId)
            .OrderByDescending(o => o.OrderDate)
            .ToListAsync();

        return _mapper.Map<List<OrderDto>>(orders);
    }

    public async Task<OrderDto> CancelOrderAsync(Guid userId, Guid orderId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(o => o.Id == orderId && o.UserId == userId);

        if (order == null)
        {
            throw new KeyNotFoundException("Order not found");
        }

        // Check if order can be cancelled
        if (order.Status != "Pending")
        {
            throw new InvalidOperationException("Only pending orders can be cancelled");
        }

        // Update order status
        order.Status = "Cancelled";
        
        // Return items to inventory
        foreach (var item in order.OrderItems)
        {
            item.Book.StockQuantity += item.Quantity;
        }

        // Save changes
        await _context.SaveChangesAsync();

        return _mapper.Map<OrderDto>(order);
    }

    public async Task<OrderDto> ProcessOrderAsync(string staffId, ClaimCodeProcessDto claimCodeProcessDto)
    {
        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .Include(o => o.User)
            .FirstOrDefaultAsync(o => o.ClaimCode == claimCodeProcessDto.ClaimCode);

        if (order == null)
        {
            throw new KeyNotFoundException("Order not found with the provided claim code");
        }

        // Check if order can be processed
        if (order.Status != "Pending")
        {
            throw new InvalidOperationException("Only pending orders can be processed");
        }

        // Update order
        order.Status = "Completed";
        order.ProcessedBy = staffId;
        order.ProcessedDate = DateTime.UtcNow;

        // Update user's successful order count
        var user = order.User;
        user.SuccessfulOrder++;
        
        // Check if user qualifies for loyalty discount
        if (user.SuccessfulOrder % 10 == 0)
        {
            user.HasStackableDiscount = true;
        }

        // Update book sold count
        foreach (var item in order.OrderItems)
        {
            item.Book.SoldCount += item.Quantity;
            
            // Broadcast successful order
            await BroadcastSuccessfulOrder(order.Id, user.Id, item.Book.Title, user.FullName);
        }

        // Save changes
        await _context.SaveChangesAsync();

        return _mapper.Map<OrderDto>(order);
    }

    public async Task<List<OrderDto>> GetPendingOrdersAsync()
    {
        var orders = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .Include(o => o.User)
            .Where(o => o.Status == "Pending")
            .OrderBy(o => o.OrderDate)
            .ToListAsync();

        return _mapper.Map<List<OrderDto>>(orders);
    }

    public async Task<OrderDto> ApplyDiscountsAsync(Guid orderId, Guid userId)
    {
        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(o => o.Id == orderId && o.UserId == userId);

        if (order == null)
        {
            throw new KeyNotFoundException("Order not found");
        }

        // Check if order can be modified
        if (order.Status != "Pending")
        {
            throw new InvalidOperationException("Only pending orders can be modified");
        }

        // Reset discounts
        order.AppliedBulkDiscount = false;
        order.AppliedLoyaltyDiscount = false;
        
        // Recalculate base amounts
        decimal totalAmount = 0;
        decimal itemDiscountAmount = 0;

        foreach (var item in order.OrderItems)
        {
            decimal unitPrice = item.UnitPrice;
            decimal discountedPrice = unitPrice - (unitPrice * item.DiscountPercentage / 100);
            decimal subtotal = discountedPrice * item.Quantity;
            
            totalAmount += unitPrice * item.Quantity;
            itemDiscountAmount += (unitPrice - discountedPrice) * item.Quantity;
        }

        order.TotalAmount = totalAmount;
        order.DecimalAmount = itemDiscountAmount;
        order.FinalAmount = totalAmount - itemDiscountAmount;

        // Check for bulk discount (5+ books)
        int totalQuantity = order.OrderItems.Sum(i => i.Quantity);
        if (totalQuantity >= 5)
        {
            decimal bulkDiscount = order.FinalAmount * 0.05m;
            order.AppliedBulkDiscount = true;
            order.DecimalAmount += bulkDiscount;
            order.FinalAmount -= bulkDiscount;
        }

        // Check for loyalty discount
        var user = await _context.Users.FindAsync(userId);
        if (user != null && user.HasStackableDiscount)
        {
            decimal loyaltyDiscount = order.FinalAmount * 0.1m;
            order.AppliedLoyaltyDiscount = true;
            order.DecimalAmount += loyaltyDiscount;
            order.FinalAmount -= loyaltyDiscount;
        }

        // Save changes
        await _context.SaveChangesAsync();

        return _mapper.Map<OrderDto>(order);
    }

    public async Task<string> GenerateClaimCodeAsync()
    {
        // Generate a random claim code with format: BS-XXXXXX-YY (where X is alphanumeric and Y is numeric)
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var code = new StringBuilder("BS-");
        
        for (int i = 0; i < 6; i++)
        {
            code.Append(chars[_random.Next(chars.Length)]);
        }
        
        code.Append("-");
        
        for (int i = 0; i < 2; i++)
        {
            code.Append(_random.Next(10));
        }
        
        // Check if code already exists
        while (await _context.Orders.AnyAsync(o => o.ClaimCode == code.ToString()))
        {
            // Regenerate code
            code.Clear();
            code.Append("BS-");
            
            for (int i = 0; i < 6; i++)
            {
                code.Append(chars[_random.Next(chars.Length)]);
            }
            
            code.Append("-");
            
            for (int i = 0; i < 2; i++)
            {
                code.Append(_random.Next(10));
            }
        }
        
        return code.ToString();
    }

    public async Task SendOrderConfirmationAsync(Guid orderId)
    {
        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .ThenInclude(i => i.Book)
            .Include(o => o.User)
            .FirstOrDefaultAsync(o => o.Id == orderId);

        if (order == null)
        {
            throw new KeyNotFoundException("Order not found");
        }

        // In a real application, this would send an email
        // For this implementation, we'll just log the confirmation
        Console.WriteLine($"Order confirmation email sent to {order.User.EmailAddress}");
        Console.WriteLine($"Claim Code: {order.ClaimCode}");
        Console.WriteLine($"Total Amount: {order.FinalAmount:C}");
        Console.WriteLine("Items:");
        
        foreach (var item in order.OrderItems)
        {
            Console.WriteLine($"- {item.Book.Title} x {item.Quantity} = {item.SubTotal:C}");
        }
    }

    // Helper method to broadcast successful order
    private async Task BroadcastSuccessfulOrder(Guid orderId, Guid userId, string bookTitle, string memberName)
    {
        await _hubContext.Clients.Group("all").SendAsync("ReceiveNotification", new
        {
            type = "order",
            message = $"{memberName} just purchased '{bookTitle}'!",
            orderId = orderId,
            userId = userId,
            timestamp = DateTime.UtcNow
        });
    }
}
