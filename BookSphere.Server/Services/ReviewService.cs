using System;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class ReviewService : IReviewService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IUserService _userService;

    public ReviewService(
        BookSphereDbContext context,
        IMapper mapper,
        IUserService userService)
    {
        _context = context;
        _mapper = mapper;
        _userService = userService;
    }

    public async Task<List<ReviewDto>> GetBookReviewsAsync(Guid bookId)
    {
        // Check if book exists
        var book = await _context.Books.FindAsync(bookId);
        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Get reviews for the book
        var reviews = await _context.Reviews
            .Include(r => r.User)
            .Where(r => r.BookId == bookId)
            .OrderByDescending(r => r.ReviewDate)
            .ToListAsync();

        return _mapper.Map<List<ReviewDto>>(reviews);
    }

    public async Task<ReviewDto> GetReviewAsync(Guid reviewId)
    {
        var review = await _context.Reviews
            .Include(r => r.User)
            .FirstOrDefaultAsync(r => r.Id == reviewId);

        if (review == null)
        {
            throw new KeyNotFoundException("Review not found");
        }

        return _mapper.Map<ReviewDto>(review);
    }

    public async Task<ReviewDto> CreateReviewAsync(Guid userId, CreateReviewDto createReviewDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Check if book exists
        var book = await _context.Books.FindAsync(createReviewDto.BookId);
        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Check if user has purchased the book
        if (!await CanReviewBookAsync(userId, createReviewDto.BookId))
        {
            throw new InvalidOperationException("You can only review books you have purchased");
        }

        // Check if user has already reviewed this book
        var existingReview = await _context.Reviews
            .FirstOrDefaultAsync(r => r.UserId == userId && r.BookId == createReviewDto.BookId);

        if (existingReview != null)
        {
            throw new InvalidOperationException("You have already reviewed this book");
        }

        // Create new review
        var review = _mapper.Map<Review>(createReviewDto);
        review.UserId = userId;
        review.ReviewDate = DateTime.UtcNow;

        // Add to database
        await _context.Reviews.AddAsync(review);
        await _context.SaveChangesAsync();

        // Return with user details
        return await GetReviewAsync(review.Id);
    }

    public async Task<ReviewDto> UpdateReviewAsync(Guid userId, Guid reviewId, CreateReviewDto updateReviewDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get review
        var review = await _context.Reviews.FindAsync(reviewId);
        if (review == null)
        {
            throw new KeyNotFoundException("Review not found");
        }

        // Check if user owns the review
        if (review.UserId != userId)
        {
            throw new UnauthorizedAccessException("You can only update your own reviews");
        }

        // Update review
        review.Rating = updateReviewDto.Rating;
        review.Comment = updateReviewDto.Comment;
        review.ReviewDate = DateTime.UtcNow;

        // Save changes
        await _context.SaveChangesAsync();

        // Return updated review
        return await GetReviewAsync(review.Id);
    }

    public async Task<bool> DeleteReviewAsync(Guid userId, Guid reviewId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get review
        var review = await _context.Reviews.FindAsync(reviewId);
        if (review == null)
        {
            throw new KeyNotFoundException("Review not found");
        }

        // Check if user owns the review
        if (review.UserId != userId)
        {
            throw new UnauthorizedAccessException("You can only delete your own reviews");
        }

        // Remove review
        _context.Reviews.Remove(review);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> CanReviewBookAsync(Guid userId, Guid bookId)
    {
        // Check if user has purchased the book
        return await _context.OrderItems
            .Include(oi => oi.Order)
            .AnyAsync(oi => 
                oi.BookId == bookId && 
                oi.Order.UserId == userId && 
                oi.Order.Status == "Completed");
    }

    public async Task<double> GetBookAverageRatingAsync(Guid bookId)
    {
        // Check if book exists
        var book = await _context.Books.FindAsync(bookId);
        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Get average rating
        var reviews = await _context.Reviews
            .Where(r => r.BookId == bookId)
            .ToListAsync();

        if (reviews.Count == 0)
        {
            return 0;
        }

        return reviews.Average(r => r.Rating);
    }
}
