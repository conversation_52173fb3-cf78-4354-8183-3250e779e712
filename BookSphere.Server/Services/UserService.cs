using System;
using System.Security.Cryptography;
using System.Text;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class UserService : IUserService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IJwtService _jwtService;

    public UserService(BookSphereDbContext context, IMapper mapper, IJwtService jwtService)
    {
        _context = context;
        _mapper = mapper;
        _jwtService = jwtService;
    }

    public async Task<AuthResponse> RegisterAsync(RegisterDto register)
    {
        // Check if email already exists
        if (await _context.Users.AnyAsync(u => u.EmailAddress == register.Email))
        {
            throw new ArgumentException("Email already registered");
        }

        // Create new user
        var user = _mapper.Map<User>(register);
        
        // Hash password
        user.PasswordHash = HashPassword(register.Password);
        
        // Generate membership ID
        user.MembershipId = GenerateMembershipId();
        
        // Create whitelist and cart for the user
        user.WhiteList = new WhiteList
        {
            CreatedDate = DateTime.UtcNow
        };
        
        user.Cart = new Cart
        {
            LastUpdated = DateTime.UtcNow
        };

        // Save user to database
        await _context.Users.AddAsync(user);
        await _context.SaveChangesAsync();

        // Generate JWT token
        var token = _jwtService.GenerateToken(user);

        // Return auth response
        return new AuthResponse
        {
            Token = token,
            User = _mapper.Map<UserDto>(user)
        };
    }

    public async Task<AuthResponse> LoginAsync(LoginDto login)
    {
        // Find user by email
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.EmailAddress == login.Email);

        // Check if user exists and password is correct
        if (user == null || !VerifyPassword(login.Password, user.PasswordHash))
        {
            throw new UnauthorizedAccessException("Invalid email or password");
        }

        // Generate JWT token
        var token = _jwtService.GenerateToken(user);

        // Return auth response
        return new AuthResponse
        {
            Token = token,
            User = _mapper.Map<UserDto>(user)
        };
    }

    public async Task<UserDto> GetUserProfileAsync(Guid userId)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        return _mapper.Map<UserDto>(user);
    }

    public async Task<UserDto> UpdateUserProfileAsync(Guid userId, UserDto userDto)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        // Update user properties
        user.FullName = userDto.FullName;
        user.Phone = userDto.PhoneNumber;
        user.Address = userDto.Address;

        // Save changes
        await _context.SaveChangesAsync();

        return _mapper.Map<UserDto>(user);
    }

    public async Task<bool> AssignRoleAsync(Guid userId, string role)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        // Validate role
        if (role != "Admin" && role != "Staff" && role != "Member")
        {
            throw new ArgumentException("Invalid role");
        }

        user.Role = role;
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> IfUserExist(Guid userId)
    {
        return await _context.Users.AnyAsync(u => u.Id == userId);
    }

    public async Task<int> GetSuccessfulOrderCount(Guid userId)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        return user.SuccessfulOrder;
    }

    public async Task<bool> HasStackableDiscount(Guid userId)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        return user.HasStackableDiscount;
    }

    public async Task<bool> UseStackableDiscount(Guid userId)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            throw new KeyNotFoundException("User not found");
        }

        if (!user.HasStackableDiscount)
        {
            return false;
        }

        user.HasStackableDiscount = false;
        await _context.SaveChangesAsync();

        return true;
    }

    // Helper methods
    private string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    private bool VerifyPassword(string password, string hash)
    {
        return HashPassword(password) == hash;
    }

    private string GenerateMembershipId()
    {
        // Generate a random membership ID with format: BS-XXXXXX (where X is alphanumeric)
        var random = new Random();
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var id = new StringBuilder("BS-");
        
        for (int i = 0; i < 6; i++)
        {
            id.Append(chars[random.Next(chars.Length)]);
        }
        
        return id.ToString();
    }
}
