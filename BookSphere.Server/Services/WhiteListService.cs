using System;
using AutoMapper;
using BookSphere.Data;
using BookSphere.DTOs;
using BookSphere.IServices;
using BookSphere.Models;
using Microsoft.EntityFrameworkCore;

namespace BookSphere.Services;

public class WhiteListService : IWhiteListService
{
    private readonly BookSphereDbContext _context;
    private readonly IMapper _mapper;
    private readonly IUserService _userService;
    private readonly ICartService _cartService;

    public WhiteListService(
        BookSphereDbContext context, 
        IMapper mapper, 
        IUserService userService,
        ICartService cartService)
    {
        _context = context;
        _mapper = mapper;
        _userService = userService;
        _cartService = cartService;
    }

    public async Task<WhiteListDto> GetWhiteListAsync(Guid userId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get whitelist with items and books
        var whitelist = await _context.WhiteLists
            .Include(w => w.WhiteListItems)
            .ThenInclude(i => i.Book)
            .FirstOrDefaultAsync(w => w.UserId == userId);

        if (whitelist == null)
        {
            throw new KeyNotFoundException("Whitelist not found");
        }

        // Map to DTO
        return _mapper.Map<WhiteListDto>(whitelist);
    }

    public async Task<WhiteListDto> AddToWhiteListAsync(Guid userId, AddToWhiteListDto addToWhiteListDto)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Check if book exists
        var book = await _context.Books.FindAsync(addToWhiteListDto.BookId);
        if (book == null)
        {
            throw new KeyNotFoundException("Book not found");
        }

        // Get whitelist
        var whitelist = await _context.WhiteLists
            .Include(w => w.WhiteListItems)
            .FirstOrDefaultAsync(w => w.UserId == userId);

        if (whitelist == null)
        {
            // Create whitelist if it doesn't exist
            whitelist = new WhiteList
            {
                UserId = userId,
                CreatedDate = DateTime.UtcNow,
                WhiteListItems = new List<WhiteListItem>()
            };
            await _context.WhiteLists.AddAsync(whitelist);
        }

        // Check if item already exists in whitelist
        if (whitelist.WhiteListItems.Any(i => i.BookId == addToWhiteListDto.BookId))
        {
            throw new InvalidOperationException("Book already in whitelist");
        }

        // Add new item
        var newItem = new WhiteListItem
        {
            WhiteListId = whitelist.Id,
            BookId = addToWhiteListDto.BookId,
            AddedDate = DateTime.UtcNow
        };
        whitelist.WhiteListItems.Add(newItem);

        // Save changes
        await _context.SaveChangesAsync();

        // Get updated whitelist with books
        return await GetWhiteListAsync(userId);
    }

    public async Task<WhiteListDto> RemoveFromWhiteListAsync(Guid userId, Guid bookId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get whitelist
        var whitelist = await _context.WhiteLists
            .Include(w => w.WhiteListItems)
            .FirstOrDefaultAsync(w => w.UserId == userId);

        if (whitelist == null)
        {
            throw new KeyNotFoundException("Whitelist not found");
        }

        // Find whitelist item
        var whitelistItem = whitelist.WhiteListItems.FirstOrDefault(i => i.BookId == bookId);

        if (whitelistItem == null)
        {
            throw new KeyNotFoundException("Book not found in whitelist");
        }

        // Remove item
        whitelist.WhiteListItems.Remove(whitelistItem);
        _context.WhiteListsItems.Remove(whitelistItem);

        // Save changes
        await _context.SaveChangesAsync();

        // Get updated whitelist with books
        return await GetWhiteListAsync(userId);
    }

    public async Task<bool> IsInWhiteListAsync(Guid userId, Guid bookId)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Get whitelist
        var whitelist = await _context.WhiteLists
            .Include(w => w.WhiteListItems)
            .FirstOrDefaultAsync(w => w.UserId == userId);

        if (whitelist == null)
        {
            return false;
        }

        // Check if book is in whitelist
        return whitelist.WhiteListItems.Any(i => i.BookId == bookId);
    }

    public async Task<CartDto> MoveToCartAsync(Guid userId, Guid bookId, int quantity)
    {
        // Check if user exists
        if (!await _userService.IfUserExist(userId))
        {
            throw new KeyNotFoundException("User not found");
        }

        // Check if book is in whitelist
        if (!await IsInWhiteListAsync(userId, bookId))
        {
            throw new InvalidOperationException("Book not in whitelist");
        }

        // Add to cart
        var addToCartDto = new AddToCartDto
        {
            BookId = bookId,
            Quantity = quantity
        };
        var cartDto = await _cartService.AddToCartAsync(userId, addToCartDto);

        // Remove from whitelist
        await RemoveFromWhiteListAsync(userId, bookId);

        return cartDto;
    }
}
