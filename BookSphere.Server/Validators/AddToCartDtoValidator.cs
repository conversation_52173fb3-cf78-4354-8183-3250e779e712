using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class AddToCartDtoValidator : AbstractValidator<AddToCartDto>
{
    public AddToCartDtoValidator()
    {
        RuleFor(x => x.BookId)
            .NotEmpty().WithMessage("Book ID is required");

        RuleFor(x => x.Quantity)
            .NotEmpty().WithMessage("Quantity is required")
            .InclusiveBetween(1, 100).WithMessage("Quantity must be between 1 and 100");
    }
}
