using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class BookFilterDtoValidator : AbstractValidator<BookFIlterDto>
{
    public BookFilterDtoValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0).WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 100).WithMessage("Page size must be between 1 and 100");

        RuleFor(x => x.MinPrice)
            .GreaterThanOrEqualTo(0).When(x => x.MinPrice.HasValue)
            .WithMessage("Minimum price cannot be negative");

        RuleFor(x => x.MaxPrice)
            .GreaterThan(x => x.MinPrice ?? 0).When(x => x.MaxPrice.HasValue && x.MinPrice.HasValue)
            .WithMessage("Maximum price must be greater than minimum price");

        RuleFor(x => x.MinRating)
            .InclusiveBetween(1, 5).When(x => x.MinRating.HasValue)
            .WithMessage("Minimum rating must be between 1 and 5");

        RuleFor(x => x.SearchTerm)
            .MaximumLength(100).When(x => !string.IsNullOrEmpty(x.SearchTerm))
            .WithMessage("Search term cannot exceed 100 characters");
    }
}
