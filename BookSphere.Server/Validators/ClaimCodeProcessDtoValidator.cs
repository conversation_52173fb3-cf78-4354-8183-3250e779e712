using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class ClaimCodeProcessDtoValidator : AbstractValidator<ClaimCodeProcessDto>
{
    public ClaimCodeProcessDtoValidator()
    {
        RuleFor(x => x.ClaimCode)
            .NotEmpty().WithMessage("Claim code is required")
            .MaximumLength(50).WithMessage("Claim code cannot exceed 50 characters");

        RuleFor(x => x.MembershipId)
            .NotEmpty().WithMessage("Membership ID is required")
            .MaximumLength(50).WithMessage("Membership ID cannot exceed 50 characters");
    }
}
