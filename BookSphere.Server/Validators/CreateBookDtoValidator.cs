using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class CreateBookDtoValidator : AbstractValidator<CreateBookDto>
{
    public CreateBookDtoValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(200).WithMessage("Title cannot exceed 200 characters");

        RuleFor(x => x.Author)
            .NotEmpty().WithMessage("Author is required")
            .MaximumLength(100).WithMessage("Author cannot exceed 100 characters");

        RuleFor(x => x.Genre)
            .NotEmpty().WithMessage("Genre is required")
            .MaximumLength(50).WithMessage("Genre cannot exceed 50 characters");

        RuleFor(x => x.ISBN)
            .NotEmpty().WithMessage("ISBN is required")
            .MaximumLength(20).WithMessage("ISBN cannot exceed 20 characters")
            .Matches("^[0-9-]{10,17}$").WithMessage("ISBN must be a valid format");

        RuleFor(x => x.Description)
            .MaximumLength(2000).WithMessage("Description cannot exceed 2000 characters");

        RuleFor(x => x.Price)
            .NotEmpty().WithMessage("Price is required")
            .GreaterThan(0).WithMessage("Price must be greater than 0")
            .LessThanOrEqualTo(10000).WithMessage("Price cannot exceed 10000");

        RuleFor(x => x.PublicationDate)
            .NotEmpty().WithMessage("Publication date is required")
            .LessThanOrEqualTo(DateTime.UtcNow).WithMessage("Publication date cannot be in the future");

        RuleFor(x => x.Publisher)
            .NotEmpty().WithMessage("Publisher is required")
            .MaximumLength(50).WithMessage("Publisher cannot exceed 50 characters");

        RuleFor(x => x.Language)
            .NotEmpty().WithMessage("Language is required")
            .MaximumLength(30).WithMessage("Language cannot exceed 30 characters");

        RuleFor(x => x.Format)
            .NotEmpty().WithMessage("Format is required")
            .MaximumLength(30).WithMessage("Format cannot exceed 30 characters");

        RuleFor(x => x.StockQuantity)
            .NotEmpty().WithMessage("Stock quantity is required")
            .GreaterThanOrEqualTo(0).WithMessage("Stock quantity cannot be negative");
    }
}
