using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class CreateOrderDtoValidator : AbstractValidator<CreateOrderDto>
{
    public CreateOrderDtoValidator()
    {
        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("Order must contain at least one item")
            .Must(items => items != null && items.Count > 0).WithMessage("Order must contain at least one item");

        RuleForEach(x => x.Items).SetValidator(new OrderItemRequestDtoValidator());
    }
}

public class OrderItemRequestDtoValidator : AbstractValidator<OrderItemRequestDto>
{
    public OrderItemRequestDtoValidator()
    {
        RuleFor(x => x.BookId)
            .NotEmpty().WithMessage("Book ID is required");

        RuleFor(x => x.Quantity)
            .NotEmpty().WithMessage("Quantity is required")
            .InclusiveBetween(1, 100).WithMessage("Quantity must be between 1 and 100");
    }
}
