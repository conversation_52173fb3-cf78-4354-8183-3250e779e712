using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class SetDiscountDtoValidator : AbstractValidator<SetDiscountDto>
{
    public SetDiscountDtoValidator()
    {
        RuleFor(x => x.BookId)
            .NotEmpty().WithMessage("Book ID is required");

        RuleFor(x => x.DiscountPercentage)
            .NotEmpty().WithMessage("Discount percentage is required")
            .InclusiveBetween(1, 100).WithMessage("Discount percentage must be between 1 and 100");

        RuleFor(x => x.StartDate)
            .NotEmpty().WithMessage("Start date is required")
            .GreaterThanOrEqualTo(DateTime.UtcNow.Date).WithMessage("Start date cannot be in the past");

        RuleFor(x => x.EndDate)
            .NotEmpty().WithMessage("End date is required")
            .GreaterThan(x => x.StartDate).WithMessage("End date must be after start date");
    }
}
