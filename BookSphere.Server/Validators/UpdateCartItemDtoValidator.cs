using BookSphere.DTOs;
using FluentValidation;

namespace BookSphere.Validators;

public class UpdateCartItemDtoValidator : AbstractValidator<UpdateCartItemDto>
{
    public UpdateCartItemDtoValidator()
    {
        RuleFor(x => x.CartItemId)
            .NotEmpty().WithMessage("Cart item ID is required");

        RuleFor(x => x.Quantity)
            .NotEmpty().WithMessage("Quantity is required")
            .InclusiveBetween(1, 100).WithMessage("Quantity must be between 1 and 100");
    }
}
